.event-status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
  margin-left: 5px;
  vertical-align: middle;
}

.approved-dot {
  background-color: #4CAF50;
}

/* Ensure the event border is visible and uses the event color */
.fc-event {
  border: 1px solid var(--fc-event-border-color, #4CAF50) !important;
  border-left: 5px solid var(--fc-event-border-color, #4CAF50) !important;
  border-radius: 4px;
  padding-left: 5px;
}

.fc-event-main-content {
  display: flex;
  align-items: center;
  justify-content: space-between; /* To push the dot to the right */
  width: 100%;
}

.fc-event-title {
  flex-grow: 1;
}
