/* Import aspect ratio utilities */
@import '../shared/styles/aspect-ratio.css';

/* Dashboard Header */
.dashboard-header {
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.spacer {
  flex: 1 1 auto;
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-container p {
  margin-top: 16px;
  color: #666;
}

/* Main Dashboard */
.dashboard-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  background-color: #f5f5f5;
  min-height: calc(100vh - 64px);
}

/* Welcome Card */
.welcome-card {
  margin-bottom: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.welcome-card mat-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
}

.welcome-card mat-card-subtitle {
  color: rgba(255, 255, 255, 0.8);
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

/* Ensure cards have enough space for full titles */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

.stat-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.stat-card mat-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  margin-bottom: 16px;
  white-space: nowrap;
  overflow: visible;
  min-height: 32px;
  flex-wrap: nowrap;
}

.stat-card mat-card-title mat-icon {
  flex-shrink: 0;
  margin-right: 4px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  margin: 8px 0;
  border-radius: 8px;
  background-color: #f8f9fa;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #333;
  margin-bottom: 4px;
}

.stat-number.active {
  color: #4caf50;
}

.stat-number.completed {
  color: #2196f3;
}

.stat-number.pending {
  color: #ff9800;
}

.stat-number.approved {
  color: #4caf50;
}

.stat-number.rejected {
  color: #f44336;
}

.stat-label {
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Content Section */
.content-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

@media (max-width: 1024px) {
  .content-section {
    grid-template-columns: 1fr;
  }
}

.content-card {
  height: fit-content;
}

.content-card mat-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: nowrap;
  white-space: nowrap;
  overflow: visible;
  min-height: 32px;
}

.content-card mat-card-title mat-icon {
  flex-shrink: 0;
  margin-right: 4px;
}

/* Content List */
.content-list {
  max-height: 600px;
  overflow-y: auto;
}

.content-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  background-color: white;
  transition: box-shadow 0.2s ease;
}

.content-item:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.content-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  flex: 1;
}

.content-description {
  color: #666;
  margin: 8px 0;
  font-size: 14px;
  line-height: 1.4;
}

.content-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin: 12px 0;
  font-size: 12px;
  color: #666;
}

.content-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.content-meta mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

/* Enhanced Content Preview Section */
.content-preview-section {
  margin: 16px 0;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #2196f3;
}

.content-description-container h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.content-description {
  color: #666;
  font-size: 14px;
  line-height: 1.6;
  max-height: 120px;
  overflow-y: auto;
  margin-bottom: 8px;
}

.tags-section {
  margin-top: 12px;
}

.tags-section h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.tags-section mat-chip-listbox {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tags-section mat-chip {
  background: #e3f2fd;
  color: #1976d2;
  font-size: 12px;
}

/* Enhanced Meta Information */
.content-meta-enhanced {
  margin: 16px 0;
  padding: 12px;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.meta-row {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  margin-bottom: 8px;
}

.meta-row:last-child {
  margin-bottom: 0;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #555;
}

.meta-item mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  color: #666;
}

.meta-item strong {
  color: #333;
  margin-right: 4px;
}

/* Enhanced Media Preview */
.media-preview-enhanced {
  margin: 16px 0;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.media-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.media-info {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
}

.media-info mat-chip {
  background: #e8f5e8;
  color: #2e7d32;
  font-size: 11px;
  height: 24px;
}

/* Legacy Media Preview (for backward compatibility) */
.media-preview {
  margin: 12px 0;
  text-align: center;
}

.content-image,
.content-video {
  max-width: 200px;
  max-height: 150px;
  border-radius: 8px;
  object-fit: cover;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: transform 0.2s ease;
}

.content-image:hover,
.content-video:hover {
  transform: scale(1.05);
  cursor: pointer;
}

.content-file {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 8px;
  color: #666;
  font-size: 14px;
}

.content-file mat-icon {
  color: #999;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  flex-wrap: wrap;
}

.action-buttons button {
  flex: 1;
  min-width: 100px;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.empty-state mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

/* Creators Overview */
.creators-overview-card {
  margin-bottom: 24px;
}

.creators-table-container {
  overflow-x: auto;
}

.creators-table {
  width: 100%;
  min-width: 600px;
}

.creator-info {
  display: flex;
  flex-direction: column;
}

.creator-name {
  font-weight: 500;
  margin-bottom: 2px;
}

.creator-email {
  font-size: 12px;
  color: #666;
}

.post-count,
.pending-count {
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: #f5f5f5;
}

.pending-count {
  position: relative;
}

/* Action Shortcuts */
.action-shortcuts-card {
  margin-bottom: 24px;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.action-grid button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .content-section {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-buttons button {
    width: 100%;
  }

  .content-meta {
    flex-direction: column;
    gap: 8px;
  }

  .action-grid {
    grid-template-columns: 1fr;
  }
}

/* Snackbar Styles */
::ng-deep .success-snackbar {
  background-color: #4caf50 !important;
  color: white !important;
}

::ng-deep .error-snackbar {
  background-color: #f44336 !important;
  color: white !important;
}

/* Material Design Overrides */
.mat-mdc-card {
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
}

.mat-mdc-chip {
  font-size: 12px !important;
}

.mat-mdc-chip mat-icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
}

/* List Styles */
.mat-mdc-list-item {
  border-bottom: 1px solid #f0f0f0;
}

.mat-mdc-list-item:last-child {
  border-bottom: none;
}

/* Badge Positioning */
.mat-badge-content {
  font-size: 10px !important;
  font-weight: 600 !important;
}

/* Scrollbar Styling */
.content-list::-webkit-scrollbar {
  width: 6px;
}

.content-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.content-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.content-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Tabbed Interface Styles */
.main-content-card {
  min-height: 600px;
  margin-bottom: 24px;
}

.dashboard-tabs {
  height: 100%;
}

.tab-content {
  padding: 24px;
  min-height: 500px;
}

/* Project Management Styles */
.projects-header {
  margin-bottom: 32px;
  text-align: center;
}

.projects-header h2 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin: 0 0 8px 0;
  font-size: 1.5rem;
  font-weight: 500;
  color: #333;
}

.projects-header p {
  color: #666;
  margin: 0;
  font-size: 1rem;
}

.projects-container {
  max-width: 1000px;
  margin: 0 auto;
}

.projects-accordion {
  margin-bottom: 20px;
}

.project-panel {
  margin-bottom: 16px;
  border-radius: 8px !important;
  overflow: hidden;
}

.project-title {
  font-weight: 500;
  margin-left: 8px;
}

.deadline {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.875rem;
  color: #666;
  margin-left: auto;
}

.project-details {
  padding: 16px 0;
}

.project-info {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.project-info h4 {
  margin: 0 0 12px 0;
  font-size: 1.1rem;
  font-weight: 500;
  color: #333;
}

.project-info p {
  margin: 0 0 12px 0;
  line-height: 1.5;
  color: #666;
}

.project-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  font-size: 0.875rem;
  color: #666;
}

.project-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.creators-section {
  margin-top: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 12px;
}

.section-header h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  color: #333;
}

.empty-creators {
  text-align: center;
  padding: 32px;
  color: #666;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px dashed #dee2e6;
}

.empty-creators mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  color: #ccc;
}

.creators-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.creator-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.creator-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.creator-card mat-card-title {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 4px;
}

.creator-card mat-card-subtitle {
  font-size: 0.875rem;
  color: #666;
}

.creator-stats {
  display: flex;
  gap: 16px;
  margin-top: 12px;
  flex-wrap: wrap;
}

.creator-stats .stat {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.875rem;
  color: #666;
  padding: 4px 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.creator-stats .stat mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

/* Enhanced responsive design for project management */
@media (max-width: 768px) {
  .tab-content {
    padding: 16px;
  }

  .projects-header h2 {
    font-size: 1.25rem;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .creators-grid {
    grid-template-columns: 1fr;
  }

  .project-meta {
    flex-direction: column;
    gap: 8px;
  }

  .creator-stats {
    justify-content: center;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Tab group styling */
.mat-mdc-tab-group {
  --mdc-tab-indicator-active-indicator-color: #1976d2;
}

.mat-mdc-tab {
  min-width: 120px;
}

/* Expansion panel styling */
.mat-expansion-panel {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
  border-radius: 8px !important;
  margin-bottom: 16px !important;
}

.mat-expansion-panel-header {
  padding: 16px 24px !important;
}

.mat-expansion-panel-content {
  padding: 0 24px !important;
}