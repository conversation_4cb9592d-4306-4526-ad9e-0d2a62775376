import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { PostService } from '../../services/post.service';
import { CreatorDashboardService } from '../../services/creator-dashboard.service';

export interface PostDialogData {
  date: string;
  projectId?: number;
  assignedProjects?: any[];
}

interface AspectRatio {
  label: string;
  value: string;
  width: number;
  height: number;
}

@Component({
  selector: 'app-post-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSnackBarModule,
    MatIconModule,
    MatChipsModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './post-dialog.component.html',
  styleUrls: ['./post-dialog.component.css']
})
export class PostDialogComponent implements OnInit {
  postForm: FormGroup;
  selectedFiles: File[] = [];
  previewUrls: string[] = [];
  assignedProjects: any[] = [];
  isSubmitting = false;

  // Aspect ratio options
  aspectRatios: AspectRatio[] = [
    { label: 'Square (1:1)', value: '1:1', width: 400, height: 400 },
    { label: 'Portrait (4:5)', value: '4:5', width: 400, height: 500 },
    { label: 'Landscape (16:9)', value: '16:9', width: 400, height: 225 },
    { label: 'Story (9:16)', value: '9:16', width: 225, height: 400 }
  ];
  selectedAspectRatio = '1:1';
  previewDimensions = { width: 400, height: 400 };

  constructor(
    private fb: FormBuilder,
    private postService: PostService,
    private creatorDashboardService: CreatorDashboardService,
    private snackBar: MatSnackBar,
    public dialogRef: MatDialogRef<PostDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: PostDialogData
  ) {
    // Parse the date from data.date (format: YYYY-MM-DD)
    const selectedDate = data.date ? new Date(data.date) : new Date();
    const defaultTime = '10:00'; // Default to 10:00 AM

    this.postForm = this.fb.group({
      title: ['', [Validators.required, Validators.minLength(3)]],
      description: ['', [Validators.required, Validators.minLength(10)]],
      project: [data.projectId || '', Validators.required],
      scheduled_date: [selectedDate, Validators.required],
      scheduled_time: [defaultTime, Validators.required],
      tags: [''],
      content: ['']
    });
  }

  ngOnInit(): void {
    // Load assigned projects if not provided
    if (this.data.assignedProjects) {
      this.assignedProjects = this.data.assignedProjects;
    } else {
      this.loadAssignedProjects();
    }
  }

  private loadAssignedProjects(): void {
    this.creatorDashboardService.getAssignedProjects().subscribe({
      next: (projects) => {
        this.assignedProjects = projects;
        if (projects.length > 0 && !this.postForm.get('project')?.value) {
          this.postForm.patchValue({ project: projects[0].id });
        }
      },
      error: (error) => {
        console.error('Error loading assigned projects:', error);
        this.snackBar.open('Error loading projects', 'Close', { duration: 3000 });
      }
    });
  }

  onFileSelected(event: any): void {
    const files = Array.from(event.target.files) as File[];

    for (const file of files) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'video/mp4', 'video/webm'];
      if (!allowedTypes.includes(file.type)) {
        this.snackBar.open(`${file.name}: Please select a valid image or video file`, 'Close', { duration: 3000 });
        continue;
      }

      // Validate file size (50MB limit)
      const maxSize = 50 * 1024 * 1024; // 50MB
      if (file.size > maxSize) {
        this.snackBar.open(`${file.name}: File size must be less than 50MB`, 'Close', { duration: 3000 });
        continue;
      }

      this.selectedFiles.push(file);

      // Generate preview
      const reader = new FileReader();
      reader.onload = () => {
        this.previewUrls.push(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  }

  removeFile(index: number): void {
    this.selectedFiles.splice(index, 1);
    this.previewUrls.splice(index, 1);
  }

  getFileType(file: File): string {
    return file.type.startsWith('image') ? 'image' : 'video';
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  getFilePreviewUrl(file: File): string {
    const index = this.selectedFiles.indexOf(file);
    return this.previewUrls[index] || '';
  }

  onAspectRatioChange(): void {
    const ratio = this.aspectRatios.find(r => r.value === this.selectedAspectRatio);
    if (ratio) {
      this.previewDimensions = { width: ratio.width, height: ratio.height };
    }
  }

  getAspectRatioStyle(): any {
    return {
      'width': this.previewDimensions.width + 'px',
      'height': this.previewDimensions.height + 'px',
      'border': '2px dashed #ccc',
      'display': 'flex',
      'align-items': 'center',
      'justify-content': 'center',
      'background-color': '#f5f5f5'
    };
  }

  onSubmit(): void {
    if (this.postForm.invalid) {
      this.markFormGroupTouched();
      this.snackBar.open('Please fill out all required fields', 'Close', { duration: 3000 });
      return;
    }

    if (this.selectedFiles.length === 0) {
      this.snackBar.open('Please select at least one media file', 'Close', { duration: 3000 });
      return;
    }

    this.isSubmitting = true;
    const formValue = this.postForm.value;

    // Combine date and time into a datetime string
    const scheduledDate = new Date(formValue.scheduled_date);
    const [hours, minutes] = formValue.scheduled_time.split(':');
    scheduledDate.setHours(parseInt(hours), parseInt(minutes), 0, 0);

    const formData = new FormData();
    formData.append('title', formValue.title);
    formData.append('description', formValue.description);
    formData.append('project', formValue.project.toString());
    formData.append('scheduled_time', scheduledDate.toISOString());

    // Add optional fields
    if (formValue.tags) {
      formData.append('tags', formValue.tags);
    }
    if (formValue.content) {
      formData.append('content', formValue.content);
    }

    // Add aspect ratio
    formData.append('aspect_ratio', this.selectedAspectRatio);

    // Add the first selected file as media
    if (this.selectedFiles.length > 0) {
      formData.append('media', this.selectedFiles[0], this.selectedFiles[0].name);
    }

    console.log('Submitting post data:', {
      title: formValue.title,
      description: formValue.description,
      project: formValue.project,
      scheduled_time: scheduledDate.toISOString(),
      tags: formValue.tags,
      content: formValue.content,
      media: this.selectedFiles[0]?.name
    });

    this.creatorDashboardService.createPost(formData).subscribe({
      next: (response) => {
        console.log('Post created successfully:', response);
        this.snackBar.open('Post created successfully!', 'Close', { duration: 3000 });
        this.dialogRef.close(response);
      },
      error: (error) => {
        console.error('Failed to create post:', error);
        let errorMessage = 'Failed to create post';
        if (error.error && error.error.error) {
          errorMessage += ': ' + error.error.error;
        } else if (error.error && typeof error.error === 'string') {
          errorMessage += ': ' + error.error;
        }
        this.snackBar.open(errorMessage, 'Close', { duration: 5000 });
        this.isSubmitting = false;
      }
    });
  }

  private markFormGroupTouched(): void {
    Object.keys(this.postForm.controls).forEach(key => {
      this.postForm.get(key)?.markAsTouched();
    });
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
