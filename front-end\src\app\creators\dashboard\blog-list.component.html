<div class="blog-list-container">
  <h2>My Blogs</h2>
  <div *ngIf="isLoading">Loading blogs...</div>
  <div *ngIf="!isLoading && blogs.length === 0">No blogs submitted yet.</div>
  <div *ngFor="let blog of blogs" class="blog-card">
    <div class="blog-header">
      <span class="blog-title">{{ blog.title }}</span>
      <span class="blog-status" [style.background]="getStatusColor(blog.status)">{{ blog.status | titlecase }}</span>
    </div>
    <div class="blog-meta">
      <span *ngIf="blog.project_detail">Project: {{ blog.project_detail.title || blog.project_detail.name }}</span>
      <span *ngIf="blog.submitted_at">Submitted: {{ blog.submitted_at | date:'medium' }}</span>
    </div>
    <div class="blog-content-preview">
      {{ blog.content | slice:0:120 }}<span *ngIf="blog.content.length > 120">...</span>
    </div>
    <div *ngIf="blog.review_comments" class="blog-review-comments">
      <strong>Review Comments:</strong> {{ blog.review_comments }}
      <div *ngIf="blog.reviewed_by_detail && blog.reviewed_at">
        <small>By {{ blog.reviewed_by_detail.full_name || blog.reviewed_by_detail.username }} on {{ blog.reviewed_at | date:'medium' }}</small>
      </div>
    </div>
  </div>
</div> 