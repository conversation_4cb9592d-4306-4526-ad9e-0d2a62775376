/* Import aspect ratio utilities */
@import '../../../shared/styles/aspect-ratio.css';

.blog-view-modal {
  max-width: 800px;
  width: 100%;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #e0e0e0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin: -24px -24px 0 -24px;
}

.blog-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 500;
}

.close-button {
  color: white;
}

.modal-content {
  padding: 24px 0;
  max-height: 70vh;
  overflow-y: auto;
}

.meta-card,
.media-card,
.scripture-card,
.content-card,
.tags-card,
.review-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.meta-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 8px;
}

.meta-label {
  font-weight: 500;
  color: #666;
}

.status-badge {
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.875rem;
  font-weight: 500;
}

.cover-image-container {
  text-align: center;
  padding: 16px;
}

.cover-image {
  max-width: 100%;
  max-height: 400px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.scripture-content {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #667eea;
  font-style: italic;
  line-height: 1.6;
}

.blog-content {
  line-height: 1.8;
  font-size: 1rem;
  color: #333;
}

.blog-content h1,
.blog-content h2,
.blog-content h3,
.blog-content h4,
.blog-content h5,
.blog-content h6 {
  margin-top: 24px;
  margin-bottom: 12px;
  color: #2c3e50;
}

.blog-content p {
  margin-bottom: 16px;
}

.blog-content img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 16px 0;
}

.blog-content blockquote {
  border-left: 4px solid #667eea;
  padding-left: 16px;
  margin: 16px 0;
  font-style: italic;
  background: #f8f9fa;
  padding: 16px;
  border-radius: 0 8px 8px 0;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tags-list mat-chip {
  background: #e3f2fd;
  color: #1976d2;
}

.review-content {
  background: #fff3e0;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #ff9800;
}

.review-comments h4 {
  margin: 0 0 8px 0;
  color: #e65100;
}

.review-comments p {
  margin: 0 0 16px 0;
  line-height: 1.6;
}

.review-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.reviewer-info,
.review-date {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: #666;
}

.modal-actions {
  padding: 16px 24px;
  border-top: 1px solid #e0e0e0;
  margin: 0 -24px -24px -24px;
  background: #fafafa;
}

/* Responsive Design */
@media (max-width: 768px) {
  .blog-view-modal {
    max-width: 95vw;
  }

  .modal-header {
    padding: 12px 16px;
  }

  .blog-title {
    font-size: 1.25rem;
  }

  .meta-grid {
    grid-template-columns: 1fr;
  }

  .modal-content {
    max-height: 60vh;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .modal-header {
    background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
  }

  .meta-item {
    background: #2d3748;
    color: #e2e8f0;
  }

  .meta-label {
    color: #a0aec0;
  }

  .scripture-content {
    background: #2d3748;
    color: #e2e8f0;
    border-left-color: #4299e1;
  }

  .blog-content {
    color: #e2e8f0;
  }

  .blog-content h1,
  .blog-content h2,
  .blog-content h3,
  .blog-content h4,
  .blog-content h5,
  .blog-content h6 {
    color: #f7fafc;
  }

  .review-content {
    background: #2d3748;
    color: #e2e8f0;
    border-left-color: #ed8936;
  }

  .review-comments h4 {
    color: #ed8936;
  }

  .modal-actions {
    background: #2d3748;
    border-top-color: #4a5568;
  }
}
