<div class="post-view-modal">
  <!-- Header -->
  <div class="modal-header">
    <h2 mat-dialog-title class="post-title">
      <mat-icon>post_add</mat-icon>
      {{ post.title }}
    </h2>
    <button mat-icon-button (click)="onClose()" class="close-button">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <!-- Content -->
  <mat-dialog-content class="modal-content">
    <!-- Post Meta Information -->
    <mat-card class="meta-card">
      <mat-card-content>
        <div class="meta-grid">
          <!-- Status -->
          <div class="meta-item">
            <mat-icon [style.color]="getStatusColor(post.status)">{{ getStatusIcon(post.status) }}</mat-icon>
            <span class="meta-label">Status:</span>
            <span class="status-badge" [style.background-color]="getStatusColor(post.status)">
              {{ post.status | titlecase }}
            </span>
          </div>

          <!-- Creator -->
          <div class="meta-item" *ngIf="post.creator_name">
            <mat-icon>person</mat-icon>
            <span class="meta-label">Creator:</span>
            <span>{{ post.creator_name }}</span>
          </div>

          <!-- Project -->
          <div class="meta-item" *ngIf="post.project_detail">
            <mat-icon>work</mat-icon>
            <span class="meta-label">Project:</span>
            <span>{{ post.project_detail.title || post.project_detail.name }}</span>
          </div>

          <!-- Company -->
          <div class="meta-item" *ngIf="post.project_detail?.company_detail">
            <mat-icon>business</mat-icon>
            <span class="meta-label">Company:</span>
            <span>{{ post.project_detail?.company_detail?.name }}</span>
          </div>

          <!-- Scheduled Date -->
          <div class="meta-item" *ngIf="post.scheduled_date">
            <mat-icon>event</mat-icon>
            <span class="meta-label">Scheduled:</span>
            <span>{{ getFormattedDate(post.scheduled_date) }}</span>
          </div>

          <!-- Created Date -->
          <div class="meta-item" *ngIf="post.created_at">
            <mat-icon>create</mat-icon>
            <span class="meta-label">Created:</span>
            <span>{{ getFormattedDate(post.created_at) }}</span>
          </div>

          <!-- Updated Date -->
          <div class="meta-item" *ngIf="post.updated_at">
            <mat-icon>update</mat-icon>
            <span class="meta-label">Updated:</span>
            <span>{{ getFormattedDate(post.updated_at) }}</span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Media -->
    <mat-card class="media-card" *ngIf="post.media_url">
      <mat-card-header>
        <mat-card-title>Media</mat-card-title>
        <mat-card-subtitle *ngIf="post.aspect_ratio">
          <span class="aspect-ratio-chip">{{ post.aspect_ratio === 'auto' ? 'Original Aspect Ratio' : post.aspect_ratio }}</span>
        </mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="post-media-container" 
             [ngClass]="post.aspect_ratio ? 'aspect-' + post.aspect_ratio.replace(':', '-') : 'aspect-auto'">
          <img *ngIf="isImage(post.media_url)" 
               [src]="post.media_url" 
               [alt]="post.title" 
               class="aspect-ratio-media">
          <video *ngIf="isVideo(post.media_url)" 
                 [src]="post.media_url" 
                 controls 
                 class="aspect-ratio-media">
          </video>
          <div *ngIf="!isImage(post.media_url) && !isVideo(post.media_url)" class="file-container">
            <mat-icon>attach_file</mat-icon>
            <span>{{ getFileName(post.media_url) }}</span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Description -->
    <mat-card class="content-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>description</mat-icon>
          Description
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="post-description">{{ post.description }}</div>
      </mat-card-content>
    </mat-card>

    <!-- Tags -->
    <mat-card class="tags-card" *ngIf="getTagsArray().length > 0">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>label</mat-icon>
          Tags
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <mat-chip-listbox class="tags-list">
          <mat-chip *ngFor="let tag of getTagsArray()">{{ tag }}</mat-chip>
        </mat-chip-listbox>
      </mat-card-content>
    </mat-card>

    <!-- Review Information -->
    <mat-card class="review-card" *ngIf="post.review_comments || post.reviewed_by_detail">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>rate_review</mat-icon>
          Review Information
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="review-content">
          <div *ngIf="post.review_comments" class="review-comments">
            <h4>Comments:</h4>
            <p>{{ post.review_comments }}</p>
          </div>
          <div *ngIf="post.reviewed_by_detail && post.reviewed_at" class="review-meta">
            <div class="reviewer-info">
              <mat-icon>person</mat-icon>
              <span>Reviewed by {{ post.reviewed_by_detail.full_name || post.reviewed_by_detail.username }}</span>
            </div>
            <div class="review-date">
              <mat-icon>schedule</mat-icon>
              <span>{{ getFormattedDate(post.reviewed_at) }}</span>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </mat-dialog-content>

  <!-- Actions -->
  <mat-dialog-actions align="end" class="modal-actions">
    <button mat-button (click)="onClose()">
      <mat-icon>close</mat-icon>
      Close
    </button>
  </mat-dialog-actions>
</div>
