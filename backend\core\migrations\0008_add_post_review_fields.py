# Generated manually for post review enhancement
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0007_project_company_admin_alter_project_deadline'),
    ]

    operations = [
        migrations.AddField(
            model_name='post',
            name='review_comments',
            field=models.TextField(blank=True, help_text='Comments from company admin during review', null=True),
        ),
        migrations.AddField(
            model_name='post',
            name='reviewed_by',
            field=models.ForeignKey(blank=True, limit_choices_to={'role': 'company_admin'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_posts', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='post',
            name='reviewed_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='post',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, null=True),
        ),
        migrations.AddField(
            model_name='post',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, null=True),
        ),
    ]
