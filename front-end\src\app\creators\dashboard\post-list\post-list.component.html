<div class="post-list-container">
  <div class="post-list-header">
    <h2>My Posts</h2>
    <p class="subtitle">Posts from your assigned projects</p>
  </div>

  <div *ngIf="posts.length === 0" class="no-posts-message">
    <mat-icon>assignment</mat-icon>
    <h3>No posts found</h3>
    <p>You haven't created any posts yet, or you don't have any assigned projects.</p>
  </div>

  <div *ngFor="let post of posts" class="post-card">
    <div class="post-header">
      <div class="post-title-section">
        <h3 class="post-title">{{ post.title }}</h3>
        <div class="post-metadata">
          <span class="project-info">
            <mat-icon class="info-icon">business</mat-icon>
            <span class="project-name">{{ post.project_name || 'Unknown Project' }}</span>
          </span>
          <span class="company-info">
            <mat-icon class="info-icon">domain</mat-icon>
            <span class="company-name">{{ getCompanyName(post) }}</span>
          </span>
        </div>
      </div>
      <div class="post-status">
        <mat-chip
          [style.background-color]="getStatusColor(post.status)"
          [style.color]="getStatusTextColor(post.status)">
          {{ getStatusLabel(post.status) }}
        </mat-chip>
      </div>
    </div>

    <div class="post-content">
      <p class="post-description">{{ post.description }}</p>

      <div class="post-details">
        <div class="date-info">
          <mat-icon class="info-icon">schedule</mat-icon>
          <span><strong>Scheduled:</strong> {{ post.scheduled_date | date:'medium' }}</span>
        </div>
        <div class="deadline-info" *ngIf="getProjectDeadline(post)">
          <mat-icon class="info-icon">event</mat-icon>
          <span><strong>Project Deadline:</strong> {{ getProjectDeadline(post) | date:'short' }}</span>
        </div>
      </div>

      <!-- Review Comments Section -->
      <div class="review-comments-section" *ngIf="post.review_comments && (post.status === 'rejected' || post.status === 'rework' || post.status === 'changes_requested')">
        <div class="review-header">
          <mat-icon class="review-icon" [style.color]="getReviewIconColor(post.status)">
            {{ getReviewIcon(post.status) }}
          </mat-icon>
          <h4 class="review-title">
            {{ getReviewTitle(post.status) }}
          </h4>
          <div class="review-meta" *ngIf="post.reviewed_by_detail && post.reviewed_at">
            <span class="reviewer-info">
              <mat-icon class="meta-icon">person</mat-icon>
              {{ post.reviewed_by_detail.full_name || post.reviewed_by_detail.username }}
            </span>
            <span class="review-date">
              <mat-icon class="meta-icon">schedule</mat-icon>
              {{ post.reviewed_at | date:'medium' }}
            </span>
          </div>
        </div>
        <div class="review-message">
          <p>{{ post.review_comments }}</p>
        </div>
        <div class="review-actions" *ngIf="post.status === 'rework' || post.status === 'changes_requested'">
          <mat-icon class="action-icon">info</mat-icon>
          <span class="action-text">Please make the requested changes and resubmit your post.</span>
        </div>
      </div>

      <!-- Media Preview -->
      <div class="media-preview" *ngIf="post.media_url">
        <div class="thumbnail-container">
          <img
            *ngIf="isImage(post.media_url)"
            [src]="post.media_url"
            [alt]="post.title"
            class="post-thumbnail"
            (click)="openMediaPreview(post)" />
          <div
            *ngIf="isVideo(post.media_url)"
            class="video-thumbnail-container"
            (click)="openMediaPreview(post)">
            <video [src]="post.media_url" class="post-thumbnail" muted></video>
            <mat-icon class="play-overlay">play_circle_filled</mat-icon>
          </div>
          <div
            *ngIf="!isImage(post.media_url) && !isVideo(post.media_url)"
            class="file-thumbnail"
            (click)="openMediaPreview(post)">
            <mat-icon class="file-icon">attach_file</mat-icon>
            <span class="file-name">{{ getFileName(post.media_url) }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="post-actions">
      <button mat-button color="primary" (click)="editPost(post)" *ngIf="post.status === 'draft' || post.status === 'rework' || post.status === 'changes_requested'">
        <mat-icon>edit</mat-icon>
        Edit
      </button>
      <button mat-button color="accent" (click)="viewPost(post)">
        <mat-icon>visibility</mat-icon>
        View
      </button>
      <button mat-raised-button color="primary" (click)="submitForReview(post)"
              *ngIf="post.status === 'draft' || post.status === 'rejected' || post.status === 'rework' || post.status === 'changes_requested'">
        <mat-icon>send</mat-icon>
        Submit for Review
      </button>
      <button mat-button color="warn" (click)="deletePost(post)" *ngIf="post.status === 'draft'">
        <mat-icon>delete</mat-icon>
        Delete
      </button>
    </div>
  </div>
</div>
