# Generated by Django 5.2 on 2025-07-10 05:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0017_add_blog_rework_status'),
    ]

    operations = [
        migrations.AddField(
            model_name='blog',
            name='aspect_ratio',
            field=models.CharField(choices=[('1:1', '1:1 (Square)'), ('16:9', '16:9 (Widescreen)'), ('4:5', '4:5 (Portrait)'), ('3:2', '3:2 (Classic)'), ('auto', 'Auto (Original)')], default='auto', help_text='Preferred aspect ratio for media display', max_length=10),
        ),
        migrations.AddField(
            model_name='post',
            name='aspect_ratio',
            field=models.CharField(choices=[('1:1', '1:1 (Square)'), ('16:9', '16:9 (Widescreen)'), ('4:5', '4:5 (Portrait)'), ('3:2', '3:2 (Classic)'), ('auto', 'Auto (Original)')], default='auto', help_text='Preferred aspect ratio for media display', max_length=10),
        ),
    ]
