import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { CommonModule } from '@angular/common';
import { CreatorDashboardService } from '../../services/creator-dashboard.service';
import { CalendarProject } from '../calendar-view/calendar-view.models';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';

@Component({
  selector: 'app-blog-create',
  standalone: true,
  templateUrl: './blog-create.component.html',
  styleUrls: ['./blog-create.component.css'],
  imports: [
    CommonModule,
    FormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatButtonModule,
    MatSelectModule,
    MatOptionModule
  ]
})
export class BlogCreateComponent implements OnInit {
  blog = {
    title: '',
    scripture: '',
    content: '',
    tags: '',
    project: null as number | null,
    previewImageUrl: '' // Add this line
  };
  coverImageFile: File | null = null;

  assignedProjects: CalendarProject[] = [];
  showPreview = false;

  constructor(private creatorDashboardService: CreatorDashboardService) {}

  ngOnInit(): void {
    this.creatorDashboardService.getAssignedProjects().subscribe(projects => {
      this.assignedProjects = projects;
    });
  }

  onSubmit() {
    this.showPreview = true; // Show preview first
  }

  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.coverImageFile = input.files[0];
      // Create a URL for the selected image to display in the preview
      const reader = new FileReader();
      reader.onload = () => {
        this.blog.previewImageUrl = reader.result as string;
      };
      reader.readAsDataURL(this.coverImageFile);
    } else {
      this.coverImageFile = null;
      this.blog.previewImageUrl = ''; // Clear preview image if no file selected
    }
  }

  confirmSubmit() {
    let authorName = 'Anonymous';
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      authorName = user.first_name || user.username || 'Anonymous';
    } catch {}

    const formData = new FormData();
    formData.append('title', this.blog.title);
    formData.append('author_name', authorName);
    formData.append('scripture', this.blog.scripture);
    formData.append('content', this.blog.content);
    formData.append('tags', this.blog.tags);
    formData.append('project', String(this.blog.project));
    if (this.coverImageFile) {
      formData.append('cover_image', this.coverImageFile);
    }
    this.creatorDashboardService.createBlog(formData).subscribe({
      next: (res) => {
        alert('Blog submitted successfully!');
        this.showPreview = false;
        this.blog = { title: '', scripture: '', content: '', tags: '', project: null, previewImageUrl: '' }; // Clear previewImageUrl
        this.coverImageFile = null;
      },
      error: (err) => {
        alert('Failed to submit blog: ' + (err.error?.detail || err.message || 'Unknown error'));
      }
    });
  }

  cancelPreview() {
    this.showPreview = false;
  }
}
