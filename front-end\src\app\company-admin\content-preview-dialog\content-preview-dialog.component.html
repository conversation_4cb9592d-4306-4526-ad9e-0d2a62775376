<h2 mat-dialog-title>{{ data.title }}</h2>
<mat-dialog-content>
  <div class="preview-section">
    <div class="meta-info">
      <p *ngIf="data.project_name"><strong>Project:</strong> {{ data.project_name }}</p>
      <p *ngIf="data.author_name"><strong>Author:</strong> {{ data.author_name }}</p>
      <p *ngIf="data.creator_name"><strong>Creator:</strong> {{ data.creator_name }}</p>
      <p><strong>Status:</strong> <mat-chip color="primary">{{ data.status | titlecase }}</mat-chip></p>
    </div>

    <div *ngIf="data.cover_image_url || data.media_url" class="image-preview">
      <img *ngIf="data.cover_image_url" [src]="data.cover_image_url" alt="Cover Image">
      <img *ngIf="data.media_url && data.media_type === 'image'" [src]="data.media_url" alt="Media Image">
      <div *ngIf="data.media_type === 'image' && data.media_width && data.media_height" class="media-dimensions text-sm text-gray-600 mt-2">
        <strong>Dimensions:</strong> {{ data.media_width }} × {{ data.media_height }} px
        <span class="aspect-ratio-info" *ngIf="getAspectRatio(data.media_width, data.media_height)">
          ({{ getAspectRatio(data.media_width, data.media_height) }})
        </span>
      </div>

      <video *ngIf="data.media_url && data.media_type === 'video'" [src]="data.media_url" controls></video>
      <div *ngIf="data.media_url && data.media_type === 'file'">
        <mat-icon>attach_file</mat-icon> File: {{ data.file_name }}
      </div>
    </div>

    <div class="content-display" [innerHTML]="data.content"></div>

    <div class="tags-section" *ngIf="data.tags && data.tags.length > 0">
      <h3>Tags:</h3>
      <mat-chip-listbox>
        <mat-chip *ngFor="let tag of data.tags.split(',')">{{ tag.trim() }}</mat-chip>
      </mat-chip-listbox>
    </div>
  </div>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-button (click)="onClose()">Close</button>
</mat-dialog-actions>