export interface DashboardStats {
  company_name: string;
  admin_name: string;
  projects: {
    total: number;
    active: number;
    completed: number;
  };
  content: {
    total_posts: number;
    pending_reviews: number;
    approved_posts: number;
    rejected_posts: number;
  };
  creators: {
    total_creators: number;
    active_creators: number;
  };
}

export interface ContentPost {
  id: number;
  title: string;
  description: string;
  media_url?: string;
  media_width?: number;
  media_height?: number;
  mediaType?: string;
  aspect_ratio?: '1:1' | '16:9' | '4:5' | '3:2' | 'auto';
  scheduled_date?: string;
  scheduled_time?: string;
  status: 'draft' | 'submitted' | 'posted' | 'rejected' | 'rework' | 'scheduled';
  creator: number;
  project: number;
  creator_name?: string;
  project_name?: string;
  review_comments?: string;
  reviewed_by?: string;
  reviewed_at?: string;
  type: 'post';
}

export interface Creator {
  id: number;
  username: string;
  full_name: string;
  email: string;
  company_name?: string;
  projects: Array<{
    id: number;
    name: string;
    title?: string;
  }>;
  total_posts: number;
  pending_posts: number;
  status: 'active' | 'idle';
  is_active?: boolean;
}

export interface ActionResponse {
  message: string;
  post_id?: number;
  new_status?: string;
  reviewed_by?: string;
  reviewed_at?: string;
  rejection_reason?: string;
  change_instructions?: string;
}

export interface NotificationItem {
  id: number;
  type: 'pending_review' | 'content_resubmission' | 'creator_feedback';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  post_id?: number;
  creator_id?: number;
}

// Project Management Interfaces
export interface AssignedProject {
  id: number;
  name: string;
  title: string;
  description?: string;
  deadline?: string;
  company: {
    id: number;
    name: string;
  };
  company_admin?: {
    id: number;
    username: string;
    full_name: string;
  };
  creators: Creator[];
  posts_count: number;
  pending_posts: number;
  created_at?: string;
  is_active?: boolean;
}

export interface CreatorAssignmentRequest {
  creator_ids: number[];
}

// Review Action Interfaces
export interface ReviewActionRequest {
  reason?: string;
  instructions?: string;
  comments?: string;
}

export interface Blog {
  id: number;
  title: string;
  author_name: string;
  author?: number;
  scripture?: string;
  content: string;
  cover_image?: string;
  media_width?: number;
  media_height?: number;
  aspect_ratio?: '1:1' | '16:9' | '4:5' | '3:2' | 'auto';
  tags?: string;
  status: 'draft' | 'submitted' | 'approved' | 'posted' | 'rejected' | 'changes_requested' | 'rework' | 'scheduled';
  submitted_at?: string;
  reviewed_by?: number;
  reviewed_at?: string;
  review_comments?: string;
  created_at?: string;
  updated_at?: string;
  project: number;
  project_detail?: any;
  creator_detail?: any;
  reviewed_by_detail?: any;
  type: 'blog';
}
