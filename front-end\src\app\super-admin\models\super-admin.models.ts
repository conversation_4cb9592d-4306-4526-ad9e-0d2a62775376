export interface SuperAdminDashboardStats {
  companies: {
    total: number;
    active: number;
  };
  admins: {
    total: number;
    active: number;
  };
  creators: {
    total: number;
    assigned: number;
  };
  content: {
    total_posts: number;
    pending_reviews: number;
  };
}

export interface Company {
  id: number;
  name: string;
  logo?: string;
  status?: 'active' | 'inactive';
  admin_count?: number;
  creator_count?: number;
  project_count?: number;
  is_active?: boolean;
  admins?: User[];
  creators?: User[];
  projects?: Project[];
}

export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  role: 'super_admin' | 'company_admin' | 'creator';
  company?: {
    id: number;
    name: string;
  };
  is_active: boolean;
  date_joined: string;
  full_name?: string;
  assigned_projects_count?: number;
}

export interface ActivityLog {
  id: string;
  type: 'admin_assignment' | 'creator_assignment' | 'company_creation' | 'user_creation';
  message: string;
  timestamp: string;
  user?: string;
  company?: string;
}

export interface AssignmentRequest {
  user_id: number;
  company_id: number;
}

export interface CreatorAssignmentRequest {
  creator_id: number;
  company_id: number;
}

export interface CompanyCreationRequest {
  name: string;
  description?: string;
  logo?: File;
}

export interface ApiResponse<T = any> {
  message: string;
  data?: T;
  error?: string;
}

export interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: string;
  action: () => void;
  color?: 'primary' | 'accent' | 'warn';
}

export interface CompanyWithDetails extends Company {
  admins: User[];
  creators: User[];
  projects: any[];
  recent_activity: ActivityLog[];
}

// Project Management Interfaces
export interface ProjectCreationRequest {
  name: string;
  title: string;
  description?: string;
  deadline?: string; // Optional for flexibility, but required in main project dialog
  company_id: number;
  creator_ids?: number[];
  company_admin_id?: number;
}

export interface Project {
  id: number;
  name: string;
  title: string;
  description?: string;
  deadline?: string;
  company: string | number;
  company_admin?: User;
  company_admin_detail?: User;
  creators: User[];
  creators_count?: number;
  created_at: string;
}

// Company Settings Interface
export interface CompanySettings {
  company: {
    id: number;
    name: string;
  };
  admin?: {
    id: number;
    username: string;
    email: string;
  };
  projects: Project[];
  available_users: User[];
}
