<div class="create-post-container">
  <mat-card class="create-post-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>{{ isEditMode ? 'edit' : 'add_circle' }}</mat-icon>
        {{ isEditMode ? 'Edit Post' : 'Create New Post' }}
      </mat-card-title>
      <mat-card-subtitle>
        {{ isEditMode ? 'Update your content based on feedback' : 'Create engaging content for your assigned projects' }}
      </mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <!-- Loading Spinner -->
      <div *ngIf="isLoading" class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading projects...</p>
      </div>

      <!-- Review Comments Section (for edit mode) -->
      <div class="review-feedback-section" *ngIf="isEditMode && editingPost?.review_comments && (editingPost?.status === 'rejected' || editingPost?.status === 'rework' || editingPost?.status === 'changes_requested')">
        <mat-card class="feedback-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon [style.color]="getReviewIconColor(editingPost?.status)">
                {{ getReviewIcon(editingPost?.status) }}
              </mat-icon>
              {{ getReviewTitle(editingPost?.status) }}
            </mat-card-title>
            <mat-card-subtitle *ngIf="editingPost?.reviewed_by_detail && editingPost?.reviewed_at">
              By {{ editingPost?.reviewed_by_detail?.full_name || editingPost?.reviewed_by_detail?.username }}
              on {{ editingPost?.reviewed_at | date:'medium' }}
            </mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <div class="feedback-message">
              <p>{{ editingPost?.review_comments }}</p>
            </div>
            <div class="feedback-action" *ngIf="editingPost?.status === 'rework' || editingPost?.status === 'changes_requested'">
              <mat-icon class="info-icon">lightbulb</mat-icon>
              <span>Please address the feedback above and resubmit your post.</span>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <form [formGroup]="postForm" (ngSubmit)="onSubmit()" *ngIf="!isLoading">

        <!-- Title Field -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Post Title</mat-label>
          <input matInput
                 formControlName="title"
                 placeholder="Enter an engaging title for your post"
                 maxlength="255">
          <mat-icon matSuffix>title</mat-icon>
          <mat-hint>{{ postForm.get('title')?.value?.length || 0 }}/255</mat-hint>
          <mat-error *ngIf="postForm.get('title')?.hasError('required')">
            Title is required
          </mat-error>
          <mat-error *ngIf="postForm.get('title')?.hasError('minlength')">
            Title must be at least 3 characters long
          </mat-error>
        </mat-form-field>

        <!-- Description Field -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Description</mat-label>
          <textarea matInput
                    formControlName="description"
                    rows="4"
                    placeholder="Describe your post content, target audience, and key messages"
                    maxlength="1000"></textarea>
          <mat-icon matSuffix>description</mat-icon>
          <mat-hint>{{ postForm.get('description')?.value?.length || 0 }}/1000</mat-hint>
          <mat-error *ngIf="postForm.get('description')?.hasError('required')">
            Description is required
          </mat-error>
          <mat-error *ngIf="postForm.get('description')?.hasError('minlength')">
            Description must be at least 10 characters long
          </mat-error>
        </mat-form-field>

        <!-- Project Selection -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Select Project</mat-label>
          <mat-select formControlName="project" placeholder="Choose a project">
            <mat-option *ngFor="let project of assignedProjects" [value]="project.id">
              <div class="project-option">
                <span class="project-name">{{ project.name }}</span>
                <span class="project-company">{{ project.company.name }}</span>
              </div>
            </mat-option>
          </mat-select>
          <mat-icon matSuffix>folder</mat-icon>
          <mat-error *ngIf="postForm.get('project')?.hasError('required')">
            Please select a project
          </mat-error>
        </mat-form-field>

        <!-- Scheduling Section -->
        <div class="scheduling-section">
          <h3>
            <mat-icon>schedule</mat-icon>
            Schedule Your Post
          </h3>

          <div class="date-time-row">
            <!-- Scheduled Date -->
            <mat-form-field appearance="outline" class="date-field">
              <mat-label>Scheduled Date</mat-label>
              <input matInput
                     [matDatepicker]="picker"
                     formControlName="scheduled_date"
                     placeholder="Choose date">
              <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
              <mat-error *ngIf="postForm.get('scheduled_date')?.hasError('required')">
                Date is required
              </mat-error>
            </mat-form-field>

            <!-- Scheduled Time -->
            <mat-form-field appearance="outline" class="time-field">
              <mat-label>Scheduled Time</mat-label>
              <input matInput
                     type="time"
                     formControlName="scheduled_time"
                     placeholder="Choose time">
              <mat-icon matSuffix>access_time</mat-icon>
              <mat-error *ngIf="postForm.get('scheduled_time')?.hasError('required')">
                Time is required
              </mat-error>
            </mat-form-field>
          </div>
        </div>

        <!-- Aspect Ratio Selection -->
        <div class="aspect-ratio-section">
          <h3>
            <mat-icon>aspect_ratio</mat-icon>
            Post Dimensions
          </h3>
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Aspect Ratio</mat-label>
            <mat-select [(ngModel)]="selectedAspectRatio" (selectionChange)="onAspectRatioChange()" name="aspectRatio">
              <mat-option *ngFor="let ratio of aspectRatios" [value]="ratio.value">
                {{ ratio.label }}
              </mat-option>
            </mat-select>
          </mat-form-field>
          <div class="aspect-ratio-preview">
            <div class="preview-frame" [ngStyle]="getAspectRatioStyle()">
              <span class="preview-text">{{ selectedAspectRatio }} Preview</span>
            </div>
          </div>
        </div>

        <!-- Media Upload Section -->
        <div class="media-upload-section">
          <h3>
            <mat-icon>perm_media</mat-icon>
            Upload Media
          </h3>

          <div class="upload-area">
            <input type="file"
                   #fileInput
                   (change)="onFileSelected($event)"
                   accept="image/*,video/*"
                   multiple
                   style="display: none;">

            <div class="upload-dropzone" (click)="fileInput.click()">
              <mat-icon class="upload-icon">cloud_upload</mat-icon>
              <p class="upload-text">Click to upload images or videos</p>
              <p class="upload-hint">Supports: JPG, PNG, GIF, MP4, WebM (Max 50MB each)</p>
            </div>
          </div>

          <!-- Selected Files Display -->
          <div *ngIf="selectedFiles.length > 0" class="selected-files">
            <h4>Selected Files ({{ selectedFiles.length }})</h4>
            <mat-chip-set>
              <mat-chip *ngFor="let file of selectedFiles; let i = index"
                        [removable]="true"
                        (removed)="removeFile(i)">
                <mat-icon matChipAvatar>{{ getFileType(file) === 'image' ? 'image' : 'videocam' }}</mat-icon>
                {{ file.name }}
                <span class="file-size">({{ formatFileSize(file.size) }})</span>
                <mat-icon matChipRemove>cancel</mat-icon>
              </mat-chip>
            </mat-chip-set>

            <!-- Media Preview with Aspect Ratio -->
            <div class="media-preview-section" *ngIf="selectedFiles.length > 0">
              <h4>Preview with {{ selectedAspectRatio }} aspect ratio:</h4>
              <div class="media-preview-container" [ngStyle]="getAspectRatioStyle()">
                <img *ngIf="getFileType(selectedFiles[0]) === 'image'"
                     [src]="getFilePreviewUrl(selectedFiles[0])"
                     alt="Preview"
                     class="preview-media">
                <video *ngIf="getFileType(selectedFiles[0]) === 'video'"
                       [src]="getFilePreviewUrl(selectedFiles[0])"
                       class="preview-media"
                       controls>
                </video>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
          <button mat-raised-button
                  type="button"
                  (click)="router.navigate(['/creators/post-list'])"
                  [disabled]="isSubmitting">
            <mat-icon>arrow_back</mat-icon>
            Cancel
          </button>

          <button mat-raised-button
                  color="primary"
                  type="submit"
                  [disabled]="postForm.invalid || (!isEditMode && selectedFiles.length === 0) || isSubmitting">
            <mat-spinner *ngIf="isSubmitting" diameter="20"></mat-spinner>
            <mat-icon *ngIf="!isSubmitting">{{ isEditMode ? 'save' : 'publish' }}</mat-icon>
            {{ isSubmitting ? (isEditMode ? 'Updating...' : 'Creating...') : (isEditMode ? 'Update Post' : 'Create Post') }}
          </button>
        </div>

      </form>
    </mat-card-content>
  </mat-card>
</div>
