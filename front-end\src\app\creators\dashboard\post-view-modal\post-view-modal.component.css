/* Import aspect ratio utilities */
@import '../../../shared/styles/aspect-ratio.css';

.post-view-modal {
  max-width: 800px;
  width: 100%;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #e0e0e0;
  background: linear-gradient(135deg, #00bcd4 0%, #2196f3 100%);
  color: white;
  margin: -24px -24px 0 -24px;
}

.post-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 500;
}

.close-button {
  color: white;
}

.modal-content {
  padding: 24px 0;
  max-height: 70vh;
  overflow-y: auto;
}

.meta-card,
.media-card,
.content-card,
.tags-card,
.review-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.meta-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 8px;
}

.meta-label {
  font-weight: 500;
  color: #666;
}

.status-badge {
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.875rem;
  font-weight: 500;
}

.post-description {
  line-height: 1.6;
  white-space: pre-wrap;
  color: #333;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #2196f3;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tags-list mat-chip {
  background: #e3f2fd;
  color: #1976d2;
}

.file-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 24px;
  background: #f5f5f5;
  border-radius: 8px;
  color: #666;
}

.review-content {
  background: #fff3e0;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #ff9800;
}

.review-comments h4 {
  margin: 0 0 8px 0;
  color: #e65100;
}

.review-comments p {
  margin: 0 0 16px 0;
  line-height: 1.6;
  white-space: pre-wrap;
}

.review-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 16px;
}

.reviewer-info,
.review-date {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: #666;
}

.modal-actions {
  padding: 16px 24px;
  border-top: 1px solid #e0e0e0;
  margin: 0 -24px -24px -24px;
  background: #fafafa;
}

/* Responsive Design */
@media (max-width: 768px) {
  .post-view-modal {
    max-width: 95vw;
  }

  .modal-header {
    padding: 12px 16px;
  }

  .post-title {
    font-size: 1.25rem;
  }

  .meta-grid {
    grid-template-columns: 1fr;
  }

  .modal-content {
    max-height: 60vh;
  }
}
