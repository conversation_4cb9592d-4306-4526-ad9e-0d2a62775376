import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FullCalendarWrapperModule } from '../../../shared/full-calendar-wrapper.module';
import dayGridPlugin from '@fullcalendar/daygrid';
import { CreatorDashboardService } from '../../services/creator-dashboard.service';
import { CalendarEvent } from './calendar-view.models';

@Component({
  selector: 'app-calendar-view',
  standalone: true,
  imports: [CommonModule, FullCalendarWrapperModule],
  templateUrl: './calendar-view.component.html',
  styleUrls: ['./calendar-view.component.css']
})
export class CalendarViewComponent implements OnInit {
  calendarPlugins = [dayGridPlugin];
  calendarEvents: any[] = [];

  constructor(private creatorDashboardService: CreatorDashboardService) {}

  ngOnInit(): void {
    this.loadCalendarData();
  }

  renderEventContent(eventInfo: any) {
    const event = eventInfo.event;
    const extendedProps = event.extendedProps;

    // Create a container div for the event content
    let container = document.createElement('div');
    container.className = 'fc-event-main-content';

    // Create the title element
    let titleEl = document.createElement('div');
    titleEl.className = 'fc-event-title';
    titleEl.innerText = event.title;
    container.appendChild(titleEl);

    // Add the green dot for approved posts
    if (extendedProps.type === 'post' && extendedProps.status === 'approved') {
      let dotEl = document.createElement('div');
      dotEl.className = 'event-status-dot approved-dot';
      container.appendChild(dotEl);
    }

    return { domNodes: [container] };
  }

  private loadCalendarData(): void {
    this.creatorDashboardService.getCalendarData().subscribe({
      next: (data) => {
        // Convert calendar events to FullCalendar format
        this.calendarEvents = data.events.map(event => ({
          title: event.title,
          date: event.date.split('T')[0], // Extract date part only
          backgroundColor: this.getEventColor(event.type, event.status),
          borderColor: this.getEventColor(event.type, event.status),
          extendedProps: {
            type: event.type,
            status: event.status,
            projectName: event.projectTitle,
            companyName: event.companyName
          }
        }));
      },
      error: (error) => {
        console.error('Error loading calendar data:', error);
      }
    });
  }

  private getEventColor(type: string, status?: string): string {
    if (type === 'post') {
      switch (status) {
        case 'draft': return '#9e9e9e';
        case 'submitted': return '#ff9800';
        case 'approved': return '#4CAF50';
        case 'posted': return '#4caf50';
        case 'rejected': return '#f44336';
        case 'rework': return '#ff5722';
        default: return '#2196f3';
      }
    } else if (type === 'project') {
      return '#673ab7';
    }
    return '#2196f3';
  }
}
