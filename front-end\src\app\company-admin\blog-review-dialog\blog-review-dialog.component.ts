import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogClose, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-blog-review-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatDialogModule
  ],
  templateUrl: './blog-review-dialog.component.html',
  styleUrl: './blog-review-dialog.component.css'
})
export class BlogReviewDialogComponent {
  comments: string = '';
  action: 'approve' | 'reject' | 'changes' | 'rework';
  blogId: number;
  currentComments: string | undefined;

  constructor(
    public dialogRef: MatDialogRef<BlogReviewDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.action = data.action;
    this.blogId = data.blogId;
    this.currentComments = data.currentComments;
    if (this.action === 'changes' && this.currentComments) {
      this.comments = this.currentComments;
    }
  }

  onAction(): void {
    this.dialogRef.close({ action: this.action, comments: this.comments });
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  getTitle(): string {
    switch (this.action) {
      case 'approve':
        return 'Approve Blog';
      case 'reject':
        return 'Reject Blog';
      case 'changes':
        return 'Request Changes for Blog';
      case 'rework':
        return 'Request Rework for Blog';
      default:
        return 'Blog Review';
    }
  }

  getButtonText(): string {
    switch (this.action) {
      case 'approve':
        return 'Approve';
      case 'reject':
        return 'Reject';
      case 'changes':
        return 'Request Changes';
      case 'rework':
        return 'Request Rework';
      default:
        return 'Submit';
    }
  }
}
