<div class="post-list-container">
<div class="blog-create-container mat-elevation-z2">
  <h1>Create New Blog</h1>

  <form (ngSubmit)="onSubmit()" #blogForm="ngForm">
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Blog Title</mat-label>
      <input matInput name="title" [(ngModel)]="blog.title" required />
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Reference Scripture (optional)</mat-label>
      <input matInput name="scripture" [(ngModel)]="blog.scripture" />
    </mat-form-field>

    <div>
      <label for="coverImage">Cover Image</label>
      <input id="coverImage" type="file" (change)="onFileSelected($event)" accept="image/*" />
    </div>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Project</mat-label>
      <mat-select name="project" [(ngModel)]="blog.project" required>
        <mat-option *ngFor="let project of assignedProjects" [value]="project.id">
          {{ project.title || project.name }} ({{ project.company.name }})
        </mat-option>
      </mat-select>
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Blog Content</mat-label>
      <textarea matInput name="content" rows="10" [(ngModel)]="blog.content" required></textarea>
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Tags</mat-label>
      <input matInput name="tags" [(ngModel)]="blog.tags" required />
    </mat-form-field>

    <button mat-raised-button color="primary" type="submit" [disabled]="!blogForm.form.valid">
      <mat-icon>send</mat-icon> Submit Blog
    </button>
    <div *ngIf="showPreview" class="preview-container mat-elevation-z4">
      <h2>Preview Blog</h2>
      <img [src]="blog.previewImageUrl" alt="Blog Image" class="preview-image" />
      <h3>{{ blog.title }}</h3>
      <p><strong>Author:</strong> (You)</p>
      <p *ngIf="blog.scripture"><strong>Scripture:</strong> {{ blog.scripture }}</p>
      <div class="blog-content">{{ blog.content }}</div>
      <p><strong>Tags:</strong> {{ blog.tags }}</p>
    
      <button mat-raised-button color="primary" (click)="confirmSubmit()">Confirm & Submit</button>
      <button mat-raised-button color="warn" (click)="cancelPreview()">Cancel</button>
    </div>
    
  </form>
</div>
</div>
