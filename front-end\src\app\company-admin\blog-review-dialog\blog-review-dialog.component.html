<h2 mat-dialog-title>{{ getTitle() }}</h2>
<mat-dialog-content>
  <p *ngIf="action === 'approve'">Are you sure you want to approve this blog?</p>
  <p *ngIf="action === 'reject'">Please provide a reason for rejecting this blog:</p>
  <p *ngIf="action === 'changes'">Please provide comments for the requested changes:</p>
  <p *ngIf="action === 'rework'">Please provide instructions for the rework:</p>

  <mat-form-field appearance="fill" *ngIf="action !== 'approve'" class="full-width">
    <mat-label>Comments</mat-label>
    <textarea matInput [(ngModel)]="comments" cdkTextareaAutosize cdkAutosizeMinRows="3" cdkAutosizeMaxRows="10"></textarea>
  </mat-form-field>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-button (click)="onCancel()">Cancel</button>
  <button mat-raised-button color="primary" (click)="onAction()" [disabled]="action !== 'approve' && !comments">{{ getButtonText() }}</button>
</mat-dialog-actions>