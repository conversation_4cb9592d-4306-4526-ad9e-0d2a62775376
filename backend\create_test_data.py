#!/usr/bin/env python
"""
Create test data for Company Admin testing
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'content_tool.settings')
django.setup()

from core.models import User, Company, Project, Post
from django.db import transaction
from datetime import datetime, timedelta

def create_test_data():
    print('Creating test data for Company Admin testing...')
    
    try:
        with transaction.atomic():
            # Clear existing data (but keep companies)
            print('Clearing existing data...')
            Post.objects.all().delete()
            Project.objects.all().delete()
            User.objects.all().delete()
            
            # Get or create companies
            print('Creating companies...')
            companies = []
            for name in ['TechCorp Solutions', 'Digital Marketing Pro', 'Creative Agency Ltd']:
                company, created = Company.objects.get_or_create(name=name)
                companies.append(company)
                print(f'  {"Created" if created else "Using existing"} company: {company.name}')
            
            # Create Super Admin
            print('Creating Super Admin...')
            super_admin = User.objects.create_user(
                username='superadmin',
                email='<EMAIL>',
                password='admin123',
                first_name='Super',
                last_name='Admin',
                role='super_admin',
                is_staff=True,
                is_superuser=True
            )
            
            # Create Company Admins
            print('Creating Company Admins...')
            company_admins = []
            admin_data = [
                ('admin_techcorp', '<EMAIL>', 'John', 'Smith', companies[0]),
                ('admin_digital', '<EMAIL>', 'Sarah', 'Johnson', companies[1]),
                ('admin_creative', '<EMAIL>', 'Mike', 'Wilson', companies[2])
            ]
            
            for username, email, first_name, last_name, company in admin_data:
                admin = User.objects.create_user(
                    username=username,
                    email=email,
                    password='admin123',
                    first_name=first_name,
                    last_name=last_name,
                    role='company_admin',
                    company=company
                )
                company_admins.append(admin)
                print(f'  Created admin: {admin.username} for {admin.company.name}')
            
            # Create Creators
            print('Creating Creators...')
            creators = []
            creator_data = [
                ('creator_alice', '<EMAIL>', 'Alice', 'Brown', companies[0]),
                ('creator_bob', '<EMAIL>', 'Bob', 'Davis', companies[0]),
                ('creator_carol', '<EMAIL>', 'Carol', 'Miller', companies[1]),
                ('creator_david', '<EMAIL>', 'David', 'Garcia', companies[1]),
                ('creator_eve', '<EMAIL>', 'Eve', 'Martinez', companies[2])
            ]
            
            for username, email, first_name, last_name, company in creator_data:
                creator = User.objects.create_user(
                    username=username,
                    email=email,
                    password='creator123',
                    first_name=first_name,
                    last_name=last_name,
                    role='creator',
                    company=company
                )
                creators.append(creator)
                print(f'  Created creator: {creator.username} for {creator.company.name}')
            
            # Create Projects and assign creators
            print('Creating Projects...')
            projects_data = [
                ('Q1_Social_Campaign', 'Q1 Social Media Campaign', 'Social media content for Q1 product launch', 30, companies[0], company_admins[0], [creators[0], creators[1]]),
                ('Tech_Blog_Series', 'Technical Blog Series', 'Weekly technical blog posts', 45, companies[0], company_admins[0], [creators[0]]),
                ('Client_Content_Pack', 'Client Content Package', 'Monthly content package for key client', 20, companies[1], company_admins[1], [creators[2], creators[3]]),
                ('Brand_Refresh', 'Brand Refresh Campaign', 'Complete brand refresh for major client', 60, companies[2], company_admins[2], [creators[4]])
            ]
            
            projects = []
            for name, title, description, days, company, admin, project_creators in projects_data:
                project = Project.objects.create(
                    name=name,
                    title=title,
                    description=description,
                    deadline=datetime.now() + timedelta(days=days),
                    company=company,
                    created_by=admin
                )
                project.creators.set(project_creators)
                projects.append(project)
                print(f'  Created project: {project.title} for {project.company.name}')
                print(f'    Assigned creators: {[c.username for c in project_creators]}')
            
            # Create some test posts
            print('Creating test posts...')
            post_data = [
                (projects[0], creators[0], 'Q1 Launch Post 1', 'First post for Q1 campaign', 'submitted'),
                (projects[0], creators[1], 'Q1 Launch Post 2', 'Second post for Q1 campaign', 'submitted'),
                (projects[1], creators[0], 'Tech Blog: AI Trends', 'Blog post about AI trends', 'draft'),
                (projects[2], creators[2], 'Client Newsletter', 'Monthly newsletter content', 'submitted'),
                (projects[2], creators[3], 'Social Media Update', 'Social media content for client', 'submitted'),
                (projects[3], creators[4], 'Brand Refresh Concept', 'Initial brand refresh concept', 'draft'),
            ]
            
            for project, creator, title, description, status in post_data:
                post = Post.objects.create(
                    project=project,
                    creator=creator,
                    title=title,
                    description=description,
                    status=status,
                    scheduled_time=datetime.now() + timedelta(days=1)
                )
                print(f'  Created post: {post.title} by {post.creator.username} ({post.status})')

        print('\n=== Test data created successfully! ===')
        print('Test accounts:')
        print('- Super Admin: superadmin / admin123')
        print('- Company Admins:')
        print('  * admin_techcorp / admin123 (TechCorp Solutions)')
        print('  * admin_digital / admin123 (Digital Marketing Pro)')
        print('  * admin_creative / admin123 (Creative Agency Ltd)')
        print('- Creators:')
        print('  * creator_alice, creator_bob / creator123 (TechCorp)')
        print('  * creator_carol, creator_david / creator123 (Digital Marketing)')
        print('  * creator_eve / creator123 (Creative Agency)')
        print('\nEach Company Admin should now see only their assigned creators and pending reviews!')
        
    except Exception as e:
        print(f'Error creating test data: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    create_test_data()
