import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatCardModule } from '@angular/material/card';
import { Post } from '../../models/post.model';

@Component({
  selector: 'app-post-view-modal',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatCardModule
  ],
  templateUrl: './post-view-modal.component.html',
  styleUrls: ['./post-view-modal.component.css']
})
export class PostViewModalComponent {
  post: Post;

  constructor(
    public dialogRef: MatDialogRef<PostViewModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { post: Post }
  ) {
    this.post = data.post;
  }

  onClose(): void {
    this.dialogRef.close();
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'submitted': return '#ff9800'; // Orange for pending
      case 'approved': return '#4caf50'; // Green
      case 'rejected': return '#f44336'; // Red
      case 'changes_requested': return '#2196f3'; // Blue
      case 'rework': return '#ff5722'; // Deep Orange for rework
      case 'draft': return '#9e9e9e'; // Grey
      case 'posted': return '#673ab7'; // Deep Purple
      case 'scheduled': return '#00bcd4'; // Cyan
      default: return '#9e9e9e'; // Default grey
    }
  }

  getStatusIcon(status: string): string {
    switch (status) {
      case 'submitted': return 'hourglass_empty';
      case 'approved': return 'check_circle';
      case 'rejected': return 'cancel';
      case 'changes_requested': return 'edit';
      case 'rework': return 'refresh';
      case 'draft': return 'draft';
      case 'posted': return 'publish';
      case 'scheduled': return 'schedule';
      default: return 'help';
    }
  }

  getTagsArray(): string[] {
    if (!this.post.tags) return [];
    return this.post.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
  }

  getFormattedDate(dateString?: string): string {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  isImage(url?: string): boolean {
    if (!url) return false;
    return /\.(jpg|jpeg|png|gif|webp)$/i.test(url);
  }

  isVideo(url?: string): boolean {
    if (!url) return false;
    return /\.(mp4|webm|ogg|avi|mov)$/i.test(url);
  }

  getMediaType(): string {
    if (!this.post.media_url) return 'none';
    if (this.isImage(this.post.media_url)) return 'image';
    if (this.isVideo(this.post.media_url)) return 'video';
    return 'file';
  }

  getFileName(url?: string): string {
    if (!url) return '';
    return url.split('/').pop() || '';
  }
}
