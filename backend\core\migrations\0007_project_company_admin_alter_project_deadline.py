# Generated by Django 5.2 on 2025-06-06 06:23

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0006_merge_20250606_1121"),
    ]

    operations = [
        migrations.AddField(
            model_name="project",
            name="company_admin",
            field=models.ForeignKey(
                blank=True,
                help_text="Assigned Company Admin for this project",
                limit_choices_to={"role": "company_admin"},
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="managed_projects",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="project",
            name="deadline",
            field=models.DateTimeField(
                help_text="Project deadline - Required field", null=True
            ),
        ),
    ]
