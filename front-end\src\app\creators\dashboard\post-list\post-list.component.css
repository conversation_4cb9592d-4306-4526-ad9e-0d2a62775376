.post-list-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.post-list-header {
  margin-bottom: 24px;
}

.post-list-header h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 28px;
  font-weight: 500;
}

.subtitle {
  color: #666;
  margin: 0;
  font-size: 16px;
}

.no-posts-message {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.no-posts-message mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  color: #ccc;
}

.post-card {
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  margin-bottom: 20px;
  background: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: box-shadow 0.3s ease;
}

.post-card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px 20px 0 20px;
}

.post-title-section {
  flex: 1;
}

.post-title {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 20px;
  font-weight: 500;
}

.post-metadata {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.project-info, .company-info {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #666;
  font-size: 14px;
}

.info-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

.post-status {
  margin-left: 16px;
}

.post-content {
  padding: 0 20px 20px 20px;
}

.post-description {
  color: #555;
  line-height: 1.6;
  margin: 16px 0;
}

.post-details {
  display: flex;
  gap: 24px;
  margin: 16px 0;
  flex-wrap: wrap;
}

.date-info, .deadline-info {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #666;
  font-size: 14px;
}

.media-preview {
  margin: 16px 0;
}

.thumbnail-container {
  position: relative;
  display: inline-block;
}

.post-thumbnail {
  max-width: 200px;
  max-height: 150px;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.post-thumbnail:hover {
  transform: scale(1.05);
}

.video-thumbnail-container {
  position: relative;
  cursor: pointer;
}

.play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 32px;
  text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}

.file-thumbnail {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border: 2px dashed #ccc;
  border-radius: 8px;
  cursor: pointer;
  max-width: 200px;
}

.file-icon {
  color: #666;
}

.file-name {
  font-size: 12px;
  color: #666;
  word-break: break-all;
}

/* Review Comments Section */
.review-comments-section {
  margin: 16px 0;
  padding: 16px;
  border-radius: 8px;
  background-color: #fafafa;
  border-left: 4px solid #ff5722;
}

.review-comments-section[data-status="rejected"] {
  border-left-color: #f44336;
  background-color: #ffebee;
}

.review-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.review-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  margin-top: 2px;
}

.review-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  flex: 1;
}

.review-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #666;
  flex-wrap: wrap;
}

.reviewer-info,
.review-date {
  display: flex;
  align-items: center;
  gap: 4px;
}

.meta-icon {
  font-size: 14px;
  width: 14px;
  height: 14px;
}

.review-message {
  margin: 12px 0;
  padding: 12px;
  background-color: white;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.review-message p {
  margin: 0;
  color: #333;
  line-height: 1.5;
  white-space: pre-wrap;
}

.review-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 12px;
  padding: 8px 12px;
  background-color: #e3f2fd;
  border-radius: 6px;
  border: 1px solid #bbdefb;
}

.action-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  color: #1976d2;
}

.action-text {
  font-size: 14px;
  color: #1976d2;
  font-weight: 500;
}

.post-actions {
  display: flex;
  gap: 8px;
  padding: 0 20px 20px 20px;
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.post-actions button {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Responsive design */
@media (max-width: 768px) {
  .post-list-container {
    padding: 16px;
  }

  .post-header {
    flex-direction: column;
    gap: 12px;
  }

  .post-metadata {
    flex-direction: column;
    gap: 8px;
  }

  .post-details {
    flex-direction: column;
    gap: 8px;
  }

  .post-actions {
    flex-wrap: wrap;
  }

  /* Review comments responsive */
  .review-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .review-meta {
    flex-direction: column;
    gap: 8px;
  }

  .review-message {
    padding: 12px;
  }

  .feedback-action {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    text-align: left;
  }
}
