import { Routes } from '@angular/router';
import { LoginComponent } from './login/login.component';
import { SuperAdminComponent } from './super-admin/super-admin.component';
import { SuperAdminDashboardComponent } from './super-admin/super-admin-dashboard.component';
import { CompanyAdminComponent } from './company-admin/company-admin.component';
import { DashboardComponent } from './creators/dashboard/dashboard.component';
import { AuthGuard } from './auth.guard';
import { RoleGuard } from './guards/role.guard';
import { CreatePostComponent } from './creators/dashboard/create-post/create-post.component';
import { PostListComponent } from './creators/dashboard/post-list/post-list.component';
import { PostDetailComponent } from './creators/dashboard/post-detail/post-detail.component';
import { CreatorDashboardCalendarViewComponent } from './creators/dashboard/calendar-view/creator-dashboard-calendar-view.component';
import { BlogCreateComponent } from './creators/dashboard/blog-create/blog-create.component';
import { BlogCreateEnhancedComponent } from './creators/dashboard/blog-create-enhanced/blog-create-enhanced.component';
import { SocialMediaConfigComponent } from './super-admin/social-media-config/social-media-config.component';


export const routes: Routes = [
  {
    path: 'super-admin',
    component: SuperAdminDashboardComponent,
    canActivate: [AuthGuard, RoleGuard],
    data: { roles: ['super_admin'] }
  },
  {
    path: 'super-admin-old',
    component: SuperAdminComponent,
    canActivate: [AuthGuard, RoleGuard],
    data: { roles: ['super_admin'] }
  },
  {
    path: 'super-admin/social-media-config',
    component: SocialMediaConfigComponent,
    canActivate: [AuthGuard, RoleGuard],
    data: { roles: ['super_admin'] }
  },
  {
    path: 'company-admin',
    component: CompanyAdminComponent,
    canActivate: [AuthGuard, RoleGuard],
    data: { roles: ['company_admin'] }
  },
  {
    path: 'creators',
    component: DashboardComponent,
    canActivate: [AuthGuard, RoleGuard],
    data: { roles: ['creator'] },
    children: [
      {
        path: 'calendar-view',
        component: CreatorDashboardCalendarViewComponent
      },
      {
        path: 'create-post',
        component: CreatePostComponent
      },
      {
        path: 'create-blog',
        component: BlogCreateComponent
      },
      {
        path: 'create-blog-enhanced',
        component: BlogCreateEnhancedComponent
      },
      {
        path: 'edit-blog/:id',
        component: BlogCreateEnhancedComponent
      },
      {
        path: 'create-post/:id',
        component: CreatePostComponent
      },
      {
        path: 'post-list',
        component: PostListComponent
      },
      {
        path: 'post/:id',
        component: PostDetailComponent
      },
      {
        path: 'my-blogs',
        loadComponent: () => import('./creators/dashboard/my-blogs/my-blogs.component').then(m => m.MyBlogsComponent)
      },
      {
        path: '',
        redirectTo: 'calendar-view',
        pathMatch: 'full'
      }
    ]
  },
  { path: '', redirectTo: 'login', pathMatch: 'full' },
  { path: 'login', component: LoginComponent },
  // { path: '**', redirectTo: 'login' }
  
];
