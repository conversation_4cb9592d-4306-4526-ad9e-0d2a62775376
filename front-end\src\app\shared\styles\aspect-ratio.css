/* Aspect Ratio Utility Classes */
.aspect-ratio-container {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Aspect ratio classes */
.aspect-1-1 { aspect-ratio: 1 / 1; }
.aspect-16-9 { aspect-ratio: 16 / 9; }
.aspect-4-5 { aspect-ratio: 4 / 5; }
.aspect-3-2 { aspect-ratio: 3 / 2; }
.aspect-auto { aspect-ratio: auto; }

/* Media content styling */
.aspect-ratio-media {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

/* Calendar thumbnail */
.calendar-thumbnail {
  width: 100%;
  max-width: 60px;
  height: 40px;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.calendar-thumbnail img,
.calendar-thumbnail video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Admin review media */
.admin-review-media {
  max-width: 300px;
  max-height: 200px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.admin-review-media img,
.admin-review-media video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Blog cover container */
.blog-cover-container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.blog-cover-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .calendar-thumbnail { max-width: 50px; height: 35px; }
  .admin-review-media { max-width: 250px; max-height: 150px; }
  .blog-cover-container { max-width: 100%; }
}

@media (max-width: 480px) {
  .calendar-thumbnail { max-width: 40px; height: 30px; }
  .admin-review-media { max-width: 200px; max-height: 120px; }
}

/* Aspect ratio chip */
.aspect-ratio-chip {
  background: #e3f2fd;
  color: #1976d2;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  display: inline-block;
  margin-bottom: 8px;
}
