import { Component, OnInit } from '@angular/core';
import { CreatorDashboardService } from '../services/creator-dashboard.service';
import { Blog } from '../models/blog.model';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-blog-list',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './blog-list.component.html',
  styleUrls: ['./blog-list.component.css']
})
export class BlogListComponent implements OnInit {
  blogs: Blog[] = [];
  isLoading = true;

  constructor(private creatorDashboardService: CreatorDashboardService) {}

  ngOnInit(): void {
    this.creatorDashboardService.getMyBlogs().subscribe({
      next: (blogs) => {
        this.blogs = blogs;
        this.isLoading = false;
      },
      error: () => {
        this.isLoading = false;
      }
    });
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'submitted': return '#ff9800'; // Orange for pending
      case 'approved': return '#4caf50'; // Green
      case 'rejected': return '#f44336'; // Red
      case 'changes_requested': return '#2196f3'; // Blue
      case 'draft': return '#9e9e9e'; // Grey
      case 'posted': return '#673ab7'; // Deep Purple
      case 'scheduled': return '#00bcd4'; // Cyan
      default: return '#9e9e9e'; // Default grey
    }
  }
} 