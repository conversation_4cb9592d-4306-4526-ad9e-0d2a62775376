#!/usr/bin/env python3
"""
Test script to verify the backend UnboundLocalError fix
"""

import os
import sys
import django
from django.conf import settings

# Add the backend directory to Python path
backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_dir)

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'content_tool.settings')
django.setup()

from django.test import RequestFactory
from django.contrib.auth import get_user_model
from core.models import Company, Project
from core.views import PostViewSet
from django.core.files.uploadedfile import SimpleUploadedFile

def test_upload_post_fix():
    """Test that the upload_post method doesn't have UnboundLocalError"""
    
    print("Testing upload_post method fix...")
    
    # Create test data
    User = get_user_model()
    
    # Create a test company
    company = Company.objects.create(
        name="Test Company",
        description="Test company for upload fix"
    )
    
    # Create a test user (creator)
    user = User.objects.create_user(
        username="testcreator",
        email="<EMAIL>",
        password="testpass123",
        role="creator",
        company=company
    )
    
    # Create a test project
    project = Project.objects.create(
        name="Test Project",
        description="Test project",
        company=company,
        company_admin=user,  # For simplicity, using same user
        deadline="2024-12-31"
    )
    project.creators.add(user)
    
    # Create a mock request
    factory = RequestFactory()
    
    # Create a simple test image file
    test_image = SimpleUploadedFile(
        "test.jpg",
        b"fake image content",
        content_type="image/jpeg"
    )
    
    # Test data
    post_data = {
        'title': 'Test Post',
        'description': 'Test description for the post',
        'project': str(project.id),
        'scheduled_time': '2024-01-15T10:00:00',
        'tags': 'test,demo',
        'content': 'Additional test content'
    }
    
    # Create POST request with file
    request = factory.post('/api/posts/upload_post/', 
                          data=post_data,
                          files={'media': test_image})
    request.user = user
    
    # Test the viewset method
    viewset = PostViewSet()
    viewset.request = request
    
    try:
        response = viewset.upload_post(request)
        print(f"✅ Success! Response status: {response.status_code}")
        if response.status_code == 201:
            print("✅ Post created successfully - UnboundLocalError is fixed!")
        else:
            print(f"⚠️  Response data: {response.data}")
        return True
    except NameError as e:
        if "data" in str(e):
            print(f"❌ UnboundLocalError still exists: {e}")
            return False
        else:
            print(f"❌ Different NameError: {e}")
            return False
    except Exception as e:
        print(f"⚠️  Other error (not UnboundLocalError): {e}")
        print("✅ UnboundLocalError is fixed, but there might be other issues")
        return True
    finally:
        # Cleanup
        try:
            project.delete()
            company.delete()
            user.delete()
        except:
            pass

if __name__ == "__main__":
    try:
        success = test_upload_post_fix()
        if success:
            print("\n🎉 Backend fix verification completed successfully!")
        else:
            print("\n❌ Backend fix verification failed!")
    except Exception as e:
        print(f"\n❌ Test setup error: {e}")
