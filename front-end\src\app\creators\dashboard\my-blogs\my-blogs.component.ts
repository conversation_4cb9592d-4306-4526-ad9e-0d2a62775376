import { Component, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CreatorDashboardService } from '../../services/creator-dashboard.service';
import { Blog } from '../../models/blog.model';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { BlogViewModalComponent } from '../blog-view-modal/blog-view-modal.component';

@Component({
  selector: 'app-my-blogs',
  standalone: true,
  imports: [CommonModule, RouterModule, MatButtonModule, MatIconModule, MatSnackBarModule, MatDialogModule],
  templateUrl: './my-blogs.component.html',
  styleUrls: ['./my-blogs.component.css']
})
export class MyBlogsComponent implements OnInit {
  blogs: Blog[] = [];
  isLoading = true;

  constructor(
    private creatorDashboardService: CreatorDashboardService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.creatorDashboardService.getMyBlogs().subscribe({
      next: (blogs: Blog[]) => {
        this.blogs = blogs;
        this.isLoading = false;
      },
      error: () => {
        this.isLoading = false;
      }
    });
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'submitted': return '#ff9800'; // Orange for pending
      case 'approved': return '#4caf50'; // Green
      case 'rejected': return '#f44336'; // Red
      case 'changes_requested': return '#2196f3'; // Blue
      case 'rework': return '#9c27b0'; // Purple for rework
      case 'draft': return '#9e9e9e'; // Grey
      case 'posted': return '#673ab7'; // Deep Purple
      case 'scheduled': return '#00bcd4'; // Cyan
      default: return '#9e9e9e'; // Default grey
    }
  }

  viewBlog(blog: Blog): void {
    const dialogRef = this.dialog.open(BlogViewModalComponent, {
      width: '90vw',
      maxWidth: '800px',
      maxHeight: '90vh',
      data: { blog: blog },
      panelClass: 'blog-view-dialog'
    });

    dialogRef.afterClosed().subscribe(result => {
      // Handle any actions after modal closes if needed
      console.log('Blog view modal closed');
    });
  }

  resubmitBlog(blogId: number): void {
    this.creatorDashboardService.resubmitBlog(blogId).subscribe({
      next: (response) => {
        this.snackBar.open('Blog resubmitted successfully!', 'Close', { duration: 3000 });
        // Refresh the blogs list
        this.ngOnInit();
      },
      error: (error) => {
        console.error('Error resubmitting blog:', error);
        this.snackBar.open('Failed to resubmit blog. Please try again.', 'Close', { duration: 5000 });
      }
    });
  }
}