.create-post-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.create-post-card {
  margin-bottom: 20px;
}

/* Aspect Ratio Section */
.aspect-ratio-section {
  margin-bottom: 24px;
}

.aspect-ratio-section h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  color: #333;
}

.aspect-ratio-preview {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

.preview-frame {
  border: 2px dashed #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 8px;
  min-height: 100px;
}

.preview-text {
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

/* Media Preview Section */
.media-preview-section {
  margin-top: 20px;
}

.media-preview-container {
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  background-color: #f9f9f9;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-media {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* Review Feedback Section */
.review-feedback-section {
  margin-bottom: 24px;
}

.feedback-card {
  border-left: 4px solid #ff5722;
  background-color: #fff3e0;
}

.feedback-card[data-status="rejected"] {
  border-left-color: #f44336;
  background-color: #ffebee;
}

.feedback-card .mat-mdc-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #333;
}

.feedback-message {
  margin: 16px 0;
  padding: 16px;
  background-color: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.feedback-message p {
  margin: 0;
  line-height: 1.6;
  color: #333;
  white-space: pre-wrap;
}

.feedback-action {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  padding: 12px;
  background-color: #e3f2fd;
  border-radius: 8px;
  border: 1px solid #bbdefb;
}

.info-icon {
  color: #1976d2;
  font-size: 20px;
  width: 20px;
  height: 20px;
}

.feedback-action span {
  color: #1976d2;
  font-weight: 500;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  text-align: center;
}

.loading-container p {
  margin-top: 16px;
  color: #666;
}

.full-width {
  width: 100%;
  margin-bottom: 16px;
}

.project-option {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.project-name {
  font-weight: 500;
}

.project-company {
  font-size: 0.875rem;
  color: #666;
}

.scheduling-section {
  margin: 24px 0;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;
}

.scheduling-section h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 500;
  color: #333;
}

.date-time-row {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.date-field {
  flex: 2;
}

.time-field {
  flex: 1;
}

.media-upload-section {
  margin: 24px 0;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;
}

.media-upload-section h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 500;
  color: #333;
}

.upload-dropzone {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fff;
}

.upload-dropzone:hover {
  border-color: #2196f3;
  background-color: #f5f5f5;
}

.upload-icon {
  font-size: 48px;
  color: #ccc;
  margin-bottom: 16px;
}

.upload-text {
  font-size: 1.1rem;
  font-weight: 500;
  margin: 0 0 8px 0;
  color: #333;
}

.upload-hint {
  font-size: 0.875rem;
  color: #666;
  margin: 0;
}

.selected-files {
  margin-top: 16px;
}

.selected-files h4 {
  margin: 0 0 12px 0;
  font-size: 1rem;
  font-weight: 500;
  color: #333;
}

.file-size {
  font-size: 0.75rem;
  color: #666;
  margin-left: 4px;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}

.action-buttons button {
  min-width: 120px;
}

/* Responsive design */
@media (max-width: 768px) {
  .create-post-container {
    padding: 16px;
  }

  .date-time-row {
    flex-direction: column;
    gap: 0;
  }

  .date-field,
  .time-field {
    flex: 1;
    width: 100%;
  }

  .action-buttons {
    flex-direction: column;
    gap: 12px;
  }

  .action-buttons button {
    width: 100%;
  }
}

/* Material theme customizations */
.mat-mdc-form-field {
  font-family: 'Roboto', sans-serif;
}

.mat-mdc-card-header .mat-mdc-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.success-snackbar {
  background-color: #4caf50 !important;
  color: white !important;
}

.error-snackbar {
  background-color: #f44336 !important;
  color: white !important;
}