from rest_framework import serializers
from .models import User, Company, Project, Post, Blog, BlogBlock
from django.utils import timezone
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'role', 'company']

class CompanySerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = ['id', 'name', 'logo']

class ProjectSerializer(serializers.ModelSerializer):
    company_detail = CompanySerializer(source='company', read_only=True)
    creators_detail = UserSerializer(source='creators', many=True, read_only=True)
    company_admin_detail = UserSerializer(source='company_admin', read_only=True)
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    company_admin = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.filter(role='company_admin'),
        required=False,
        allow_null=True
    )
    creators = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.filter(role='creator'),
        many=True,
        required=False  # <-- This fixes the issue
    )

    class Meta:
        model = Project
        fields = ['id', 'name', 'title', 'description', 'deadline', 'company', 'company_detail',
                 'company_admin', 'company_admin_detail', 'creators', 'creators_detail', 'created_at']


class PostSerializer(serializers.ModelSerializer):
    project_detail = ProjectSerializer(source='project', read_only=True)
    creator_detail = UserSerializer(source='creator', read_only=True)
    reviewed_by_detail = UserSerializer(source='reviewed_by', read_only=True)

    project = serializers.PrimaryKeyRelatedField(queryset=Project.objects.all())
    creator = serializers.PrimaryKeyRelatedField(queryset=User.objects.filter(role='creator'))
    reviewed_by = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.filter(role='company_admin'),
        required=False,
        allow_null=True
    )
    media = serializers.FileField(use_url=True, required=False)
    media_url = serializers.SerializerMethodField()
    scheduled_time = serializers.DateTimeField(required=False)

    # Add thumbnail URL for calendar view compatibility
    thumbnailUrl = serializers.SerializerMethodField()
    mediaType = serializers.SerializerMethodField()

    # Add convenience fields for dashboard
    creator_name = serializers.SerializerMethodField()
    project_name = serializers.SerializerMethodField()
    company_name = serializers.SerializerMethodField()
    project_deadline = serializers.SerializerMethodField()

    # Add project and company details for frontend
    projectTitle = serializers.SerializerMethodField()
    companyName = serializers.SerializerMethodField()

    class Meta:
        model = Post
        fields = '__all__'
        read_only_fields = ['reviewed_by', 'reviewed_at']

    def get_media_url(self, obj):
        request = self.context.get('request')
        if obj.media:
            # Use request.build_absolute_uri to get full URL with domain and protocol
            return request.build_absolute_uri(obj.media.url)
        return None

    def get_creator_name(self, obj):
        return obj.creator.get_full_name() or obj.creator.username if obj.creator else 'Unknown'

    def get_project_name(self, obj):
        return obj.project.name if obj.project else 'Unknown'

    def get_company_name(self, obj):
        return obj.project.company.name if obj.project and obj.project.company else 'Unknown Company'

    def get_project_deadline(self, obj):
        if obj.project:
            # Get the latest scheduled post as project deadline
            latest_post = Post.objects.filter(project=obj.project).order_by('-scheduled_date').first()
            if latest_post and latest_post.scheduled_date:
                return latest_post.scheduled_date.isoformat()
        return None

    def get_thumbnailUrl(self, obj):
        """Return thumbnail URL for calendar view compatibility"""
        if obj.media:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.media.url)
        return None

    def get_mediaType(self, obj):
        """Determine media type for frontend"""
        if obj.media:
            filename = obj.media.name.lower()
            if filename.endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp')):
                return 'image'
            elif filename.endswith(('.mp4', '.webm', '.ogg', '.avi', '.mov')):
                return 'video'
            else:
                return 'file'
        return None

    def get_projectTitle(self, obj):
        """Return project title for frontend compatibility"""
        return obj.project.name if obj.project else None

    def get_companyName(self, obj):
        """Return company name for frontend compatibility"""
        return obj.project.company.name if obj.project and obj.project.company else None

class BlogBlockSerializer(serializers.ModelSerializer):
    image_url = serializers.SerializerMethodField()

    class Meta:
        model = BlogBlock
        fields = ['id', 'block_type', 'content', 'image', 'image_url', 'order']

    def get_image_url(self, obj):
        request = self.context.get('request')
        if obj.image:
            return request.build_absolute_uri(obj.image.url)
        return None

class BlogSerializer(serializers.ModelSerializer):
    cover_image = serializers.ImageField(required=False, allow_null=True)
    tags = serializers.CharField(required=False, allow_blank=True)
    project_detail = ProjectSerializer(source='project', read_only=True)
    creator_detail = UserSerializer(source='author', read_only=True)
    reviewed_by_detail = UserSerializer(source='reviewed_by', read_only=True)
    blocks = BlogBlockSerializer(many=True, read_only=True)

    class Meta:
        model = Blog
        fields = '__all__'
        read_only_fields = ['author', 'status', 'created_at', 'updated_at', 'reviewed_by', 'reviewed_at']

    def create(self, validated_data):
        # Set initial status to 'submitted' and author to the current user
        validated_data['status'] = 'submitted'
        validated_data['author'] = self.context['request'].user
        return super().create(validated_data)

    def update(self, instance, validated_data):
        # Prevent direct update of status, reviewed_by, reviewed_at via this serializer
        validated_data.pop('status', None)
        validated_data.pop('reviewed_by', None)
        validated_data.pop('reviewed_at', None)
        return super().update(instance, validated_data)

class BlogReviewSerializer(serializers.ModelSerializer):
    class Meta:
        model = Blog
        fields = ['id', 'status', 'review_comments', 'reviewed_by', 'reviewed_at']

    def validate_status(self, value):
        if value not in ['approved', 'rejected', 'changes_requested', 'rework']:
            raise serializers.ValidationError("Invalid status for blog review.")
        return value

    def update(self, instance, validated_data):
        instance.status = validated_data.get('status', instance.status)
        instance.review_comments = validated_data.get('review_comments', instance.review_comments)
        instance.reviewed_by = self.context['request'].user
        instance.reviewed_at = timezone.now()
        instance.save()
        return instance

class PostReviewSerializer(serializers.ModelSerializer):
    # This is a dummy comment to force reload
    class Meta:
        model = Post
        fields = ['id', 'status', 'review_comments', 'reviewed_by', 'reviewed_at']
        read_only_fields = ['reviewed_by', 'reviewed_at']

    def validate_status(self, value):
        if value not in ['approved', 'rejected', 'rework']:
            raise serializers.ValidationError("Invalid status for post review.")
        return value

    def update(self, instance, validated_data):
        instance.status = validated_data.get('status', instance.status)
        instance.review_comments = validated_data.get('review_comments', instance.review_comments)
        instance.reviewed_by = self.context['request'].user
        instance.reviewed_at = timezone.now()
        instance.save()
        return instance

class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    # This is a dummy comment to force recompilation.
    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)

        # Add custom claims
        token['username'] = user.username
        token['role'] = user.role  # This is what Angular is decoding

        return token

    def validate(self, attrs):
        data = super().validate(attrs)

        # Add user information to the response
        user = self.user
        data['user'] = {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'role': user.role,
            'company': {
                'id': user.company.id,
                'name': user.company.name
            } if user.company else None,
            'is_active': user.is_active,
            'date_joined': user.date_joined.isoformat()
        }

        return data
