import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Post } from '../../models/post.model';
import { PostService } from '../../services/post.service';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-post-detail',
  imports: [CommonModule, MatButtonModule, MatIconModule],
  templateUrl: './post-detail.component.html',
  styleUrl: './post-detail.component.css'
})
export class PostDetailComponent implements OnInit {
  postId!: number;
  post?: Post;

  constructor(
    private route: ActivatedRoute,
    private postService: PostService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.postId = Number(this.route.snapshot.paramMap.get('id'));
    this.loadPost();
  }

  loadPost(): void {
    this.postService.getPost(this.postId).subscribe({
      next: (data) => this.post = data,
      error: () => alert('Failed to load post details.')
    });
  }

  goBack(): void {
    this.router.navigate(['/creators/post-list']);
  }

  editPost(): void {
    if (this.post?.id) {
      this.router.navigate(['/creators/create-post', this.post.id]);
    }
  }

  getStatusClass(status: string): string {
    const statusClasses: { [key: string]: string } = {
      'draft': 'text-gray-600 bg-gray-100 px-2 py-1 rounded',
      'submitted': 'text-blue-600 bg-blue-100 px-2 py-1 rounded',
      'posted': 'text-green-600 bg-green-100 px-2 py-1 rounded',
      'rejected': 'text-red-600 bg-red-100 px-2 py-1 rounded',
      'rework': 'text-orange-600 bg-orange-100 px-2 py-1 rounded',
      'scheduled': 'text-purple-600 bg-purple-100 px-2 py-1 rounded'
    };
    return statusClasses[status] || 'text-gray-600';
  }

  isImage(fileUrl: string): boolean {
    return /\.(jpg|jpeg|png|gif|webp)$/i.test(fileUrl);
  }

  isVideo(fileUrl: string): boolean {
    return /\.(mp4|webm|ogg)$/i.test(fileUrl);
  }

  getFileName(url: string): string {
    return url.split('/').pop() || 'Unknown File';
  }

}
