import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatChipsModule } from '@angular/material/chips';

import { CreatorDashboardService } from '../../services/creator-dashboard.service';
import { CalendarProject } from '../calendar-view/calendar-view.models';
import { Blog } from '../../models/blog.model';
import { BlogEditorComponent, BlogBlock } from '../blog-editor/blog-editor.component';
import { BlogPreviewComponent } from '../blog-preview/blog-preview.component';

@Component({
  selector: 'app-blog-create-enhanced',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatOptionModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatTabsModule,
    MatSnackBarModule,
    MatChipsModule,
    BlogEditorComponent,
    BlogPreviewComponent
  ],
  templateUrl: './blog-create-enhanced.component.html',
  styleUrls: ['./blog-create-enhanced.component.css']
})
export class BlogCreateEnhancedComponent implements OnInit {
  blog = {
    title: '',
    scripture: '',
    tags: '',
    project: null as number | null
  };
  
  coverImageFile: File | null = null;
  coverImageUrl: string = '';
  selectedAspectRatio: '1:1' | '16:9' | '4:5' | '3:2' | 'auto' = 'auto';
  contentBlocks: BlogBlock[] = [];
  assignedProjects: CalendarProject[] = [];

  isLoading = false;
  isSubmitting = false;
  showPreview = false;
  
  // Edit mode properties
  isEditMode = false;
  editingBlogId: number | null = null;
  originalBlog: Blog | null = null;

  constructor(
    private creatorDashboardService: CreatorDashboardService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadAssignedProjects();
    
    // Check if we're in edit mode
    this.route.params.subscribe(params => {
      if (params['id']) {
        this.editingBlogId = +params['id'];
        this.isEditMode = true;
        this.loadBlogForEditing();
      }
    });
  }

  loadAssignedProjects(): void {
    this.isLoading = true;
    this.creatorDashboardService.getAssignedProjects().subscribe({
      next: (projects: CalendarProject[]) => {
        this.assignedProjects = projects;
        this.isLoading = false;
      },
      error: (error: any) => {
        console.error('Error loading projects:', error);
        this.showError('Failed to load assigned projects');
        this.isLoading = false;
      }
    });
  }

  loadBlogForEditing(): void {
    if (!this.editingBlogId) return;

    this.isLoading = true;
    this.creatorDashboardService.getBlog(this.editingBlogId).subscribe({
      next: (blog: Blog) => {
        this.originalBlog = blog;
        this.populateFormFromBlog(blog);
        this.isLoading = false;
      },
      error: (error: any) => {
        console.error('Error loading blog:', error);
        this.showError('Failed to load blog for editing');
        this.router.navigate(['/creators/my-blogs']);
      }
    });
  }

  populateFormFromBlog(blog: Blog): void {
    this.blog = {
      title: blog.title,
      scripture: blog.scripture || '',
      tags: blog.tags || '',
      project: blog.project
    };
    
    if (blog.cover_image) {
      this.coverImageUrl = blog.cover_image;
    }

    // Set aspect ratio from blog data
    if (blog.aspect_ratio) {
      this.selectedAspectRatio = blog.aspect_ratio;
    }
    
    // Convert blog content to blocks (simplified - you might want to parse existing content)
    if (blog.content) {
      this.contentBlocks = [{
        id: 'initial_content',
        type: 'paragraph',
        content: blog.content,
        order: 0
      }];
    }
  }

  onCoverImageSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.coverImageFile = input.files[0];
      
      const reader = new FileReader();
      reader.onload = () => {
        this.coverImageUrl = reader.result as string;
      };
      reader.readAsDataURL(this.coverImageFile);
    }
  }

  onBlocksChange(blocks: BlogBlock[]): void {
    this.contentBlocks = blocks;
  }

  onPreview(): void {
    if (!this.isFormValid()) {
      this.showError('Please fill in all required fields');
      return;
    }
    this.showPreview = true;
  }

  onSubmit(): void {
    if (!this.isFormValid()) {
      this.showError('Please fill in all required fields');
      return;
    }

    this.isSubmitting = true;
    
    const formData = new FormData();
    formData.append('title', this.blog.title);
    formData.append('scripture', this.blog.scripture);
    formData.append('tags', this.blog.tags);
    formData.append('project', String(this.blog.project));
    
    // Convert blocks to content (simplified approach)
    const content = this.convertBlocksToContent();
    formData.append('content', content);

    // Add aspect ratio
    formData.append('aspect_ratio', this.selectedAspectRatio);

    // Add author name
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const authorName = user.first_name || user.username || 'Anonymous';
      formData.append('author_name', authorName);
    } catch {
      formData.append('author_name', 'Anonymous');
    }

    if (this.coverImageFile) {
      formData.append('cover_image', this.coverImageFile);
    }

    const operation = this.isEditMode
      ? this.creatorDashboardService.updateBlog(this.editingBlogId!, formData)
      : this.creatorDashboardService.createBlog(formData);

    operation.subscribe({
      next: (response: any) => {
        if (this.isEditMode) {
          // If editing a blog that was in rework or changes_requested status, automatically resubmit it
          if (this.originalBlog && ['rework', 'changes_requested'].includes(this.originalBlog.status)) {
            this.resubmitAfterUpdate();
          } else {
            this.showSuccess('Blog updated successfully!');
            this.router.navigate(['/creators/my-blogs']);
          }
        } else {
          this.showSuccess('Blog created successfully!');
          this.router.navigate(['/creators/my-blogs']);
        }
      },
      error: (error: any) => {
        console.error('Error saving blog:', error);
        this.showError('Failed to save blog. Please try again.');
        this.isSubmitting = false;
      }
    });
  }

  convertBlocksToContent(): string {
    return this.contentBlocks.map(block => {
      switch (block.type) {
        case 'heading':
          return `# ${block.content}`;
        case 'paragraph':
          return block.content;
        case 'quote':
          return `> ${block.content}`;
        case 'code':
          return `\`\`\`\n${block.content}\n\`\`\``;
        case 'image':
          return block.content ? `![${block.content}](image)` : '![Image](image)';
        default:
          return block.content;
      }
    }).join('\n\n');
  }

  isFormValid(): boolean {
    return !!(this.blog.title && this.blog.project && this.contentBlocks.length > 0);
  }

  cancelPreview(): void {
    this.showPreview = false;
  }

  cancel(): void {
    this.router.navigate(['/creators/my-blogs']);
  }

  getBlogForPreview(): Blog {
    return {
      title: this.blog.title,
      author_name: this.getAuthorName(),
      content: this.convertBlocksToContent(),
      scripture: this.blog.scripture,
      tags: this.blog.tags,
      status: 'draft',
      project: this.blog.project || 0,
      cover_image: this.coverImageUrl
    } as Blog;
  }

  private getAuthorName(): string {
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      return user.first_name || user.username || 'Anonymous';
    } catch {
      return 'Anonymous';
    }
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  private resubmitAfterUpdate(): void {
    if (!this.editingBlogId) {
      this.showError('Unable to resubmit: Blog ID not found');
      this.isSubmitting = false;
      return;
    }

    this.creatorDashboardService.resubmitBlog(this.editingBlogId).subscribe({
      next: (response) => {
        this.showSuccess('Blog updated and resubmitted successfully!');
        this.router.navigate(['/creators/my-blogs']);
      },
      error: (error) => {
        console.error('Error resubmitting blog:', error);
        this.showError('Blog was updated but failed to resubmit. You can manually resubmit from My Blogs.');
        this.router.navigate(['/creators/my-blogs']);
      }
    });
  }
}
