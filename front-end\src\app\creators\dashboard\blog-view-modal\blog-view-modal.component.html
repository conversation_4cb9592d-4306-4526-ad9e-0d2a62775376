<div class="blog-view-modal">
  <!-- Header -->
  <div class="modal-header">
    <h2 mat-dialog-title class="blog-title">
      <mat-icon>article</mat-icon>
      {{ blog.title }}
    </h2>
    <button mat-icon-button (click)="onClose()" class="close-button">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <!-- Content -->
  <mat-dialog-content class="modal-content">
    <!-- Blog Meta Information -->
    <mat-card class="meta-card">
      <mat-card-content>
        <div class="meta-grid">
          <!-- Status -->
          <div class="meta-item">
            <mat-icon [style.color]="getStatusColor(blog.status)">{{ getStatusIcon(blog.status) }}</mat-icon>
            <span class="meta-label">Status:</span>
            <span class="status-badge" [style.background-color]="getStatusColor(blog.status)">
              {{ blog.status | titlecase }}
            </span>
          </div>

          <!-- Author -->
          <div class="meta-item">
            <mat-icon>person</mat-icon>
            <span class="meta-label">Author:</span>
            <span>{{ blog.author_name }}</span>
          </div>

          <!-- Project -->
          <div class="meta-item" *ngIf="blog.project_detail">
            <mat-icon>work</mat-icon>
            <span class="meta-label">Project:</span>
            <span>{{ blog.project_detail.title || blog.project_detail.name }}</span>
          </div>

          <!-- Submitted Date -->
          <div class="meta-item" *ngIf="blog.submitted_at">
            <mat-icon>schedule</mat-icon>
            <span class="meta-label">Submitted:</span>
            <span>{{ getFormattedDate(blog.submitted_at) }}</span>
          </div>

          <!-- Created Date -->
          <div class="meta-item" *ngIf="blog.created_at">
            <mat-icon>create</mat-icon>
            <span class="meta-label">Created:</span>
            <span>{{ getFormattedDate(blog.created_at) }}</span>
          </div>

          <!-- Updated Date -->
          <div class="meta-item" *ngIf="blog.updated_at">
            <mat-icon>update</mat-icon>
            <span class="meta-label">Updated:</span>
            <span>{{ getFormattedDate(blog.updated_at) }}</span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Cover Image -->
    <mat-card class="media-card" *ngIf="blog.cover_image">
      <mat-card-header>
        <mat-card-title>Cover Image</mat-card-title>
        <mat-card-subtitle *ngIf="blog.aspect_ratio">
          <span class="aspect-ratio-chip">{{ blog.aspect_ratio === 'auto' ? 'Original Aspect Ratio' : blog.aspect_ratio }}</span>
        </mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="blog-cover-container"
             [ngClass]="blog.aspect_ratio ? 'aspect-' + blog.aspect_ratio.replace(':', '-') : 'aspect-auto'">
          <img [src]="blog.cover_image" [alt]="blog.title" class="aspect-ratio-media">
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Scripture -->
    <mat-card class="scripture-card" *ngIf="blog.scripture">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>menu_book</mat-icon>
          Reference Scripture
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="scripture-content">
          {{ blog.scripture }}
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Blog Content -->
    <mat-card class="content-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>article</mat-icon>
          Blog Content
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="blog-content" [innerHTML]="blog.content"></div>
      </mat-card-content>
    </mat-card>

    <!-- Tags -->
    <mat-card class="tags-card" *ngIf="getTagsArray().length > 0">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>label</mat-icon>
          Tags
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <mat-chip-listbox class="tags-list">
          <mat-chip *ngFor="let tag of getTagsArray()">{{ tag }}</mat-chip>
        </mat-chip-listbox>
      </mat-card-content>
    </mat-card>

    <!-- Review Information -->
    <mat-card class="review-card" *ngIf="blog.review_comments || blog.reviewed_by_detail">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>rate_review</mat-icon>
          Review Information
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="review-content">
          <div *ngIf="blog.review_comments" class="review-comments">
            <h4>Comments:</h4>
            <p>{{ blog.review_comments }}</p>
          </div>
          <div *ngIf="blog.reviewed_by_detail && blog.reviewed_at" class="review-meta">
            <div class="reviewer-info">
              <mat-icon>person</mat-icon>
              <span>Reviewed by {{ blog.reviewed_by_detail.full_name || blog.reviewed_by_detail.username }}</span>
            </div>
            <div class="review-date">
              <mat-icon>schedule</mat-icon>
              <span>{{ getFormattedDate(blog.reviewed_at) }}</span>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </mat-dialog-content>

  <!-- Actions -->
  <mat-dialog-actions align="end" class="modal-actions">
    <button mat-button (click)="onClose()">
      <mat-icon>close</mat-icon>
      Close
    </button>
  </mat-dialog-actions>
</div>
