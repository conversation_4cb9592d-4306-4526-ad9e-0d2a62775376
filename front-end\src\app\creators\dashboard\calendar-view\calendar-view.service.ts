import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject, combineLatest, map } from 'rxjs';
import { PostService } from '../../services/post.service';
import { CreatorDashboardService } from '../../services/creator-dashboard.service';
import { CalendarDay, CalendarPost, CalendarFilter, PostStatus, StatusConfig, CalendarEvent, CalendarProject, BlogStatus } from './calendar-view.models';
import { Post } from '../../models/post.model';

@Injectable({
  providedIn: 'root'
})
export class CalendarViewService {
  private currentDateSubject = new BehaviorSubject<Date>(new Date());
  private selectedDateSubject = new BehaviorSubject<Date | null>(null);
  private filterSubject = new BehaviorSubject<CalendarFilter>({
    statuses: ['draft', 'submitted', 'posted', 'rejected', 'rework', 'scheduled', 'approved', 'changes_requested'],
    searchTerm: '',
    tags: [],
    projectIds: [],
    companyIds: []
  });

  currentDate$ = this.currentDateSubject.asObservable();
  selectedDate$ = this.selectedDateSubject.asObservable();
  filter$ = this.filterSubject.asObservable();

  constructor(
    private postService: PostService,
    private creatorDashboardService: CreatorDashboardService
  ) {}

  // Status configuration matching existing color scheme
  getStatusConfig(status: PostStatus | BlogStatus | 'project'): StatusConfig {
    const configs: Record<PostStatus | BlogStatus | 'project', StatusConfig> = {
      'posted': { color: '#673ab7', icon: 'done_all', label: 'Posted' },
      'scheduled': { color: '#00bcd4', icon: 'event', label: 'Scheduled' },
      'rework': { color: '#ff5722', icon: 'edit_note', label: 'Rework' },
      'draft': { color: '#9e9e9e', icon: 'create', label: 'Draft' },
      'submitted': { color: '#ff9800', icon: 'send', label: 'Submitted' },
      'rejected': { color: '#f44336', icon: 'cancel', label: 'Rejected' },
      'approved': { color: '#4caf50', icon: 'check_circle', label: 'Approved' },
      'changes_requested': { color: '#2196f3', icon: 'info', label: 'Changes Requested' },
      'project': { color: '#795548', icon: 'folder', label: 'Project Deadline' }
    };
    return configs[status];
  }

  // Date navigation methods
  navigateToMonth(date: Date): void {
    this.currentDateSubject.next(new Date(date));
  }

  navigateToPreviousMonth(): void {
    const current = this.currentDateSubject.value;
    const previous = new Date(current.getFullYear(), current.getMonth() - 1, 1);
    this.currentDateSubject.next(previous);
  }

  navigateToNextMonth(): void {
    const current = this.currentDateSubject.value;
    const next = new Date(current.getFullYear(), current.getMonth() + 1, 1);
    this.currentDateSubject.next(next);
  }

  navigateToToday(): void {
    this.currentDateSubject.next(new Date());
  }

  // Date selection
  selectDate(date: Date | null): void {
    this.selectedDateSubject.next(date);
  }

  // Filter methods
  updateFilter(filter: Partial<CalendarFilter>): void {
    const currentFilter = this.filterSubject.value;
    this.filterSubject.next({ ...currentFilter, ...filter });
  }

  // Generate calendar days for month view using creator dashboard data
  generateCalendarDays(date: Date): Observable<CalendarDay[]> {
    return combineLatest([
      this.creatorDashboardService.getCalendarData(),
      this.filter$
    ]).pipe(
      map(([dashboardData, filter]) => {
        const year = date.getFullYear();
        const month = date.getMonth();

        // Get first day of month and calculate start of calendar grid
        const firstDayOfMonth = new Date(year, month, 1);
        const startOfCalendar = new Date(firstDayOfMonth);
        startOfCalendar.setDate(startOfCalendar.getDate() - firstDayOfMonth.getDay());

        // Generate 42 days (6 weeks)
        const days: CalendarDay[] = [];
        const today = new Date();
        const selectedDate = this.selectedDateSubject.value;
        
        for (let i = 0; i < 42; i++) {
          const currentDate = new Date(startOfCalendar);
          currentDate.setDate(startOfCalendar.getDate() + i);
          
          // Filter events for this day
          const dayEvents: CalendarEvent[] = [];
          dayEvents.push(...this.filterEventsForDay(dashboardData.events, currentDate, filter));
          dayEvents.push(...this.filterProjectEventsForDay(dashboardData.events, currentDate, filter));

          days.push({
            date: new Date(currentDate),
            isCurrentMonth: currentDate.getMonth() === month,
            isToday: this.isSameDay(currentDate, today),
            isSelected: selectedDate ? this.isSameDay(currentDate, selectedDate) : false,
            hasEvents: dayEvents.length > 0,
            events: dayEvents.sort((a, b) => a.start.getTime() - b.start.getTime()),
          });
        }
        
        return days;
      })
    );
  }

  private filterEventsForDay(events: CalendarEvent[], date: Date, filter: CalendarFilter): CalendarEvent[] {
    return events
      .filter(event => {
        // Filter by type (only posts and blogs for this function)
        if (event.type === 'project') return false; // Exclude projects here

        // Filter by date
        if (!event.start || !this.isSameDay(event.start, date)) return false;

        // Filter by status
        if (event.status && !filter.statuses.includes(event.status)) return false;

        // Filter by search term
        if (filter.searchTerm && !event.title.toLowerCase().includes(filter.searchTerm.toLowerCase())) {
          return false;
        }

        // Filter by project IDs
        if (filter.projectIds.length > 0 && !(event.projectId && filter.projectIds.includes(event.projectId))) {
          return false;
        }

        // Filter by company IDs
        if (filter.companyIds.length > 0 && !(event.companyId && filter.companyIds.includes(event.companyId))) {
          return false;
        }

        return true;
      });
  }

  private filterProjectEventsForDay(events: CalendarEvent[], date: Date, filter: CalendarFilter): CalendarEvent[] {
    return events
      .filter(event => {
        // Filter by type - only projects for this function
        if (event.type !== 'project') return false;

        // Filter by deadline date (using event.end for projects)
        if (!event.end || !this.isSameDay(event.end, date)) return false;

        // Filter by search term
        if (filter.searchTerm && !event.title.toLowerCase().includes(filter.searchTerm.toLowerCase())) {
          return false;
        }

        // Filter by project IDs
        if (filter.projectIds.length > 0 && !(event.projectId && filter.projectIds.includes(event.projectId))) {
          return false;
        }

        // Filter by company IDs
        if (filter.companyIds.length > 0 && !(event.companyId && filter.companyIds.includes(event.companyId))) {
          return false;
        }

        return true;
      });
  }

  private convertEventToProject(event: CalendarEvent, projects: CalendarProject[]): CalendarProject {
    const project = projects.find(p => p.id === (event.projectId ?? 0));
    return project || {
      id: (event.projectId ?? 0) as number,
      title: event.title,
      name: event.title,
      companyName: event.companyName,
      companyId: event.companyId ?? 0,
      deadline: event.end?.toISOString() || undefined, // Use event.end as deadline for projects
      postsCount: 0,
      pendingPostsCount: 0,
      company: {
        id: event.companyId ?? 0,
        name: event.companyName ?? ''
      }
    };
  }

  private convertEventToCalendarPost(event: CalendarEvent): CalendarPost {
    const scheduledDate = event.start; // Directly use event.start as it's already a Date
    return {
      id: event.id || 0,
      title: event.title,
      description: event.description || '',
      time: scheduledDate.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      }),
      status: event.status || 'draft',
      mediaType: this.getMediaType(event.mediaUrl ?? ''),
      thumbnailUrl: event.mediaUrl ?? '',
      scheduledDate,
      projectTitle: event.projectTitle || '',
      companyName: event.companyName || 'Unknown Company',
      projectId: event.projectId || 0,
      companyId: event.companyId || 0,
      deadline: event.end?.toISOString() || undefined // Use event.end as deadline for calendar posts if available
    };
  }

  private convertToCalendarPost(post: Post): CalendarPost {
    const scheduledDate = new Date(post.scheduled_date || '');
    return {
      id: post.id || 0,
      title: post.title,
      description: post.description || '',
      time: scheduledDate.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      }),
      status: post.status,
      mediaType: this.getMediaType(post.media_url ?? ''),
      thumbnailUrl: post.media_url ?? '',
      scheduledDate,
      projectTitle: post.project_name || '',
      companyName: post.project_detail?.company_detail?.name || '',
      projectId: post.project || 0,
      companyId: post.project_detail?.company_detail?.id || 0,
      deadline: post.project_deadline || undefined // Use project_deadline from Post model
    };
  }

  private getMediaType(mediaUrl: string): 'image' | 'video' | 'file' | null {
    if (!mediaUrl) return null;
    if (mediaUrl.includes('image') || /\.(jpg|jpeg|png|gif|webp)$/i.test(mediaUrl)) {
      return 'image';
    }
    if (mediaUrl.includes('video') || /\.(mp4|webm|ogg|avi|mov)$/i.test(mediaUrl)) {
      return 'video';
    }
    return 'file';
  }

  private isSameDay(date1: Date, date2: Date): boolean {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
  }

  // Utility methods
  formatMonthYear(date: Date): string {
    return date.toLocaleDateString('en-US', { 
      month: 'long', 
      year: 'numeric' 
    });
  }

  getWeekDays(): string[] {
    return ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  }
}
