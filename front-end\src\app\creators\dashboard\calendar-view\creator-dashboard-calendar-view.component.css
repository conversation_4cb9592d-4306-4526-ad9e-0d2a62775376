/* Import aspect ratio utilities */
@import '../../../shared/styles/aspect-ratio.css';

/* Container */
.calendar-view-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

/* Header Section */
.calendar-header {
  margin-bottom: 24px;
}

.header-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.month-navigation {
  display: flex;
  align-items: center;
  gap: 16px;
}

.month-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.month-title {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: #333;
  min-width: 200px;
}

.nav-button, .date-picker-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.today-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
}

/* Filters Section */
.filters-section {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 12px;
  border: 1px solid #e0e0e0;
}

.search-field {
  width: 300px;
  margin-bottom: 16px;
}

.status-filters {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.filter-label {
  font-weight: 500;
  color: #666;
  white-space: nowrap;
}

.status-chips {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.status-chip {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border-radius: 16px;
  border: 2px solid;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  font-weight: 500;
}

.status-chip:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.clear-filters {
  color: #666;
  font-size: 12px;
}

/* Calendar Card */
.calendar-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  overflow: hidden;
}

/* Calendar Grid */
.calendar-grid {
  width: 100%;
}

.week-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: #f5f5f5;
  border-bottom: 2px solid #e0e0e0;
}

.week-day-header {
  padding: 12px;
  text-align: center;
  font-weight: 600;
  color: #666;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.calendar-body {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: #e0e0e0;
}

/* Calendar Day */
.calendar-day {
  background: white;
  min-height: 120px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  display: flex;
  flex-direction: column;
}

.calendar-day:hover {
  background: #f8f9fa;
  transform: scale(1.02);
  z-index: 1;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.calendar-day.other-month {
  background: #fafafa;
  color: #ccc;
}

.calendar-day.today {
  background: #e3f2fd;
  border: 2px solid #2196f3;
}

.calendar-day.selected {
  background: #fff3e0;
  border: 2px solid #ff9800;
}

.calendar-day.has-posts {
  background: #f9f9f9;
}

.day-number {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  align-self: flex-start;
}

.calendar-day.other-month .day-number {
  color: #ccc;
}

.calendar-day.today .day-number {
  color: #2196f3;
  font-weight: 700;
}

/* Posts Container */
.posts-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
  overflow: hidden;
}

/* Post Card */
.post-card {
  background: white;
  border-radius: 6px;
  padding: 4px 6px;
  border-left: 3px solid;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

.post-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.post-time {
  font-size: 10px;
  color: #666;
  font-weight: 500;
  margin-bottom: 2px;
}

.post-content {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 2px;
}

.media-icon {
  flex-shrink: 0;
}

.media-type-icon {
  font-size: 12px;
  color: #666;
}

.post-title {
  font-size: 11px;
  font-weight: 500;
  color: #333;
  line-height: 1.2;
  flex: 1;
  overflow: hidden;
}

.status-badge {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.status-icon {
  font-size: 10px;
  color: white;
}

/* Enhanced approval indicators */
.approved-badge {
  box-shadow: 0 0 8px rgba(76, 175, 80, 0.6);
  border: 2px solid #4caf50;
  animation: approvedPulse 2s infinite;
}

.posted-badge {
  box-shadow: 0 0 8px rgba(103, 58, 183, 0.6);
  border: 2px solid #673ab7;
}

.approval-indicator {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 12px;
  height: 12px;
  background: #ffd700;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #fff;
}

.approval-star {
  font-size: 8px;
  color: #ff6f00;
}

@keyframes approvedPulse {
  0% {
    box-shadow: 0 0 8px rgba(76, 175, 80, 0.6);
  }
  50% {
    box-shadow: 0 0 16px rgba(76, 175, 80, 0.8);
  }
  100% {
    box-shadow: 0 0 8px rgba(76, 175, 80, 0.6);
  }
}

/* More Posts Indicator */
.more-posts-indicator {
  font-size: 10px;
  color: #666;
  text-align: center;
  padding: 2px;
  background: #f0f0f0;
  border-radius: 4px;
  margin-top: 2px;
}

/* Empty Day */
.empty-day-hint {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.calendar-day:hover .empty-day-hint {
  opacity: 0.3;
}

.add-icon {
  font-size: 24px;
  color: #ccc;
}

/* Floating Action Button */
.fab-new-post {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .calendar-view-container {
    padding: 16px;
  }
  
  .header-controls {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .month-navigation {
    justify-content: center;
  }
  
  .search-field {
    width: 100%;
  }
  
  .status-filters {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .calendar-day {
    min-height: 80px;
    padding: 4px;
  }
  
  .month-title {
    font-size: 20px;
    min-width: auto;
  }
  
  .post-card {
    padding: 2px 4px;
  }
  
  .post-title {
    font-size: 10px;
  }
  
  .status-chips {
    justify-content: center;
  }
  
  .fab-new-post {
    bottom: 16px;
    right: 16px;
  }
}

@media (max-width: 480px) {
  .calendar-day {
    min-height: 60px;
  }
  
  .week-day-header {
    padding: 8px 4px;
    font-size: 12px;
  }
  
  .filters-section {
    padding: 12px;
  }
  
  .status-chip {
    padding: 4px 8px;
    font-size: 11px;
  }
}

/* Thumbnail preview styles */
.thumbnail-preview {
  margin-top: 4px;
}

.thumbnail-image {
  width: 40px;
  height: 30px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.thumbnail-image:hover {
  transform: scale(1.1);
}

.video-thumbnail {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 16px;
  text-shadow: 0 1px 2px rgba(0,0,0,0.7);
}

.file-thumbnail {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 30px;
  background: #f5f5f5;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.file-thumbnail:hover {
  background: #e0e0e0;
}

.file-icon {
  font-size: 16px;
  color: #666;
}

/* Project info in posts */
.post-info {
  flex: 1;
}

.project-info {
  margin-top: 2px;
  font-size: 10px;
  color: #888;
}

.project-name {
  font-weight: 500;
  color: #555;
}

.company-name {
  color: #888;
  margin-left: 4px;
}

.company-name::before {
  content: "•";
  margin-right: 4px;
}
