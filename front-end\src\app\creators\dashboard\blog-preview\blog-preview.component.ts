import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { Blog } from '../../models/blog.model';
import { BlogBlock } from '../blog-editor/blog-editor.component';

@Component({
  selector: 'app-blog-preview',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatChipsModule
  ],
  templateUrl: './blog-preview.component.html',
  styleUrls: ['./blog-preview.component.css']
})
export class BlogPreviewComponent {
  @Input() blog: Blog | null = null;
  @Input() blocks: BlogBlock[] = [];
  @Input() coverImageUrl: string = '';
  @Input() showMetadata: boolean = true;

  parseContentToBlocks(content: string): any[] {
    if (!content) return [];
    
    // Simple markdown-like parsing
    const lines = content.split('\n');
    const blocks: any[] = [];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      if (!line) continue;
      
      if (line.startsWith('# ')) {
        blocks.push({
          type: 'heading',
          content: line.substring(2)
        });
      } else if (line.startsWith('> ')) {
        blocks.push({
          type: 'quote',
          content: line.substring(2)
        });
      } else if (line.startsWith('```')) {
        // Code block
        const codeLines = [];
        i++; // Skip opening ```
        while (i < lines.length && !lines[i].trim().startsWith('```')) {
          codeLines.push(lines[i]);
          i++;
        }
        blocks.push({
          type: 'code',
          content: codeLines.join('\n')
        });
      } else if (line.startsWith('![')) {
        // Image block
        const altMatch = line.match(/!\[(.*?)\]/);
        blocks.push({
          type: 'image',
          content: altMatch ? altMatch[1] : '',
          imageUrl: this.blog?.cover_image || ''
        });
      } else {
        blocks.push({
          type: 'paragraph',
          content: line
        });
      }
    }
    
    return blocks;
  }

  getContentBlocks(): any[] {
    if (this.blocks && this.blocks.length > 0) {
      return this.blocks;
    }
    
    if (this.blog?.content) {
      return this.parseContentToBlocks(this.blog.content);
    }
    
    return [];
  }

  getTags(): string[] {
    if (!this.blog?.tags) return [];
    return this.blog.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
  }

  getFormattedDate(dateString?: string): string {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  getAuthorName(): string {
    return this.blog?.author_name || 'Unknown Author';
  }

  getProjectName(): string {
    return this.blog?.project_detail?.title || 
           this.blog?.project_detail?.name || 
           'Unknown Project';
  }

  getCompanyName(): string {
    return this.blog?.project_detail?.company_detail?.name || 'Unknown Company';
  }
}
