.blog-preview-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.1);
  line-height: 1.6;
}

/* Cover Image */
.cover-image-section {
  margin-bottom: 32px;
  text-align: center;
}

.cover-image {
  max-width: 100%;
  max-height: 400px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
  object-fit: cover;
}

/* Blog Header */
.blog-header {
  margin-bottom: 32px;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 24px;
}

.blog-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin: 0 0 24px 0;
  line-height: 1.2;
}

.blog-metadata {
  color: #666;
}

.metadata-row {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  margin-bottom: 12px;
}

.metadata-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.95rem;
}

.metadata-item mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
  color: #999;
}

.scripture-section {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-left: 4px solid #2196f3;
  border-radius: 4px;
}

.scripture-section mat-icon {
  color: #2196f3;
}

.scripture-text {
  font-style: italic;
  color: #555;
}

/* Blog Content */
.blog-content {
  margin-bottom: 32px;
}

.content-block {
  margin-bottom: 24px;
}

.content-heading {
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin: 32px 0 16px 0;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 8px;
}

.content-paragraph {
  font-size: 1.1rem;
  color: #444;
  margin: 16px 0;
  text-align: justify;
}

.content-image-block {
  text-align: center;
  margin: 32px 0;
}

.content-image {
  max-width: 100%;
  max-height: 500px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
  object-fit: cover;
}

.image-caption {
  margin-top: 12px;
  font-style: italic;
  color: #666;
  font-size: 0.95rem;
}

.content-quote {
  position: relative;
  background-color: #f8f9fa;
  border-left: 4px solid #6c757d;
  padding: 20px 24px;
  margin: 24px 0;
  border-radius: 4px;
  font-style: italic;
  color: #555;
}

.quote-icon {
  position: absolute;
  top: 16px;
  left: -12px;
  background: white;
  color: #6c757d;
  font-size: 24px;
  width: 24px;
  height: 24px;
}

.content-quote p {
  margin: 0;
  font-size: 1.1rem;
  line-height: 1.6;
}

.content-code {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 16px;
  margin: 16px 0;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.4;
}

.content-code code {
  background: none;
  padding: 0;
  color: #333;
}

/* Tags Section */
.tags-section {
  margin-bottom: 24px;
  padding-top: 24px;
  border-top: 1px solid #e0e0e0;
}

.tags-section h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 1.2rem;
}

/* Status Section */
.status-section {
  padding-top: 24px;
  border-top: 2px solid #f0f0f0;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.status-label {
  font-weight: 600;
  color: #333;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.85rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status-draft { background-color: #e3f2fd; color: #1976d2; }
.status-submitted { background-color: #fff3e0; color: #f57c00; }
.status-approved { background-color: #e8f5e8; color: #388e3c; }
.status-rejected { background-color: #ffebee; color: #d32f2f; }
.status-changes_requested { background-color: #fff3e0; color: #f57c00; }
.status-posted { background-color: #e8f5e8; color: #388e3c; }

.review-info {
  background-color: #fff8e1;
  border-left: 4px solid #ffc107;
  padding: 16px;
  border-radius: 4px;
}

.review-info h4 {
  margin: 0 0 8px 0;
  color: #f57c00;
}

.review-comments {
  margin: 8px 0;
  color: #333;
}

.review-meta {
  margin-top: 8px;
}

.review-meta small {
  color: #666;
  font-style: italic;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 48px 24px;
  color: #666;
}

.empty-icon {
  font-size: 64px;
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
  color: #ccc;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  color: #999;
}

.empty-state p {
  margin: 0;
  color: #bbb;
}

/* Responsive Design */
@media (max-width: 768px) {
  .blog-preview-container {
    padding: 16px;
  }
  
  .blog-title {
    font-size: 2rem;
  }
  
  .content-heading {
    font-size: 1.5rem;
  }
  
  .metadata-row {
    flex-direction: column;
    gap: 8px;
  }
  
  .content-paragraph {
    font-size: 1rem;
  }
}
