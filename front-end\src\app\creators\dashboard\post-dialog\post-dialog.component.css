/* Import aspect ratio utilities */
@import '../../../shared/styles/aspect-ratio.css';

.dialog-content {
  max-height: 80vh;
  overflow-y: auto;
  padding: 20px;
}

.post-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-width: 500px;
}

.full-width {
  width: 100%;
}

.project-option {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.project-name {
  font-weight: 500;
  color: #333;
}

.project-company {
  font-size: 0.85em;
  color: #666;
}

/* Scheduling Section */
.scheduling-section {
  margin: 20px 0;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;
}

.scheduling-section h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 16px 0;
  color: #1976d2;
  font-size: 1.1em;
}

.date-time-row {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.date-field {
  flex: 1;
}

.time-field {
  flex: 1;
}



/* Aspect Ratio Section */
.aspect-ratio-section {
  margin: 20px 0;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #f8f9fa;
}

.aspect-ratio-section h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 16px 0;
  color: #1976d2;
  font-size: 1.1em;
}

.aspect-ratio-preview {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

.preview-frame {
  border: 2px dashed #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.preview-text {
  color: #666;
  font-size: 0.9em;
  text-align: center;
}

/* Media Upload Section */
.media-upload-section {
  margin: 20px 0;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #f8f9fa;
}

.media-upload-section h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 16px 0;
  color: #1976d2;
  font-size: 1.1em;
}

.upload-area {
  margin-bottom: 16px;
}

.upload-dropzone {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fafafa;
}

.upload-dropzone:hover {
  border-color: #1976d2;
  background-color: #f0f7ff;
}

.upload-icon {
  font-size: 48px;
  color: #ccc;
  margin-bottom: 16px;
}

.upload-text {
  font-size: 1.1em;
  color: #333;
  margin: 0 0 8px 0;
}

.upload-hint {
  font-size: 0.9em;
  color: #666;
  margin: 0;
}

/* Selected Files */
.selected-files {
  margin-top: 16px;
}

.selected-files h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 1em;
}

.file-size {
  font-size: 0.8em;
  color: #666;
  margin-left: 4px;
}

/* Media Preview */
.media-preview-section {
  margin-top: 20px;
}

.media-preview-section h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 1em;
}

.media-preview-container {
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.preview-media {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Dialog Actions */
.dialog-actions {
  padding: 16px 24px;
  border-top: 1px solid #e0e0e0;
}

.dialog-actions button {
  margin-left: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .post-form {
    min-width: 300px;
  }

  .date-time-row {
    flex-direction: column;
  }

  .dialog-content {
    padding: 16px;
  }
}

/* Material Design Enhancements */
mat-form-field {
  margin-bottom: 8px;
}

mat-chip-set {
  margin: 8px 0;
}

mat-chip {
  margin: 4px;
}

/* Loading States */
mat-spinner {
  margin-right: 8px;
}

/* Error States */
mat-error {
  font-size: 0.85em;
}

/* Hints */
mat-hint {
  font-size: 0.85em;
  color: #666;
}